import{_ as p,a as u,r as i,o as x,e as t,f as a,g as s,F as m,i as y,u as g,m as f,t as c}from"./index-ad117935.js";const v={class:"container mx-auto px-4 py-8 max-w-6xl"},h={key:0,class:"flex justify-center py-12"},w={key:1,class:"text-center py-12"},b={key:2,class:"space-y-8"},k={class:"md:w-1/3 bg-gray-100 p-6 flex items-center justify-center"},D=["src","alt"],B={class:"md:w-2/3 p-6"},A={class:"text-2xl font-bold mb-4"},I={key:0,class:"prose max-w-none text-gray-700"},V={class:"whitespace-pre-line"},j={key:1,class:"text-gray-500 italic"},F={__name:"BrandDetailView",setup(N){const{getApiData:d}=u(),n=i([]),r=i(!0),_=async()=>{r.value=!0;try{const e=await d("brands");e&&e.resData&&Array.isArray(e.resData)?(n.value=e.resData,document.title="品牌介紹",console.log(`成功載入 ${n.value.length} 個品牌`)):console.error("無法載入品牌資訊:",(e==null?void 0:e.message)||"未知錯誤")}catch(e){console.error("載入品牌資訊時發生錯誤:",e)}finally{r.value=!1}};return x(()=>{_()}),(e,o)=>(t(),a("div",v,[o[2]||(o[2]=s("div",{class:"mb-8 text-center"},[s("h1",{class:"text-3xl font-bold"},"品牌介紹")],-1)),r.value?(t(),a("div",h,o[0]||(o[0]=[s("div",{class:"spinner"},null,-1),s("p",{class:"ml-3 text-gray-600"},"載入中...",-1)]))):n.value.length?(t(),a("div",b,[(t(!0),a(m,null,y(n.value,l=>(t(),a("div",{key:l.id,class:"bg-white rounded-lg shadow-md overflow-hidden flex flex-col md:flex-row"},[s("div",k,[s("img",{src:g(f)(l.logo,"brand"),alt:l.name,class:"max-h-48 max-w-full object-contain",onerror:"this.src=API_CONFIG.images.placeholder"},null,8,D)]),s("div",B,[s("h2",A,c(l.name),1),l.description?(t(),a("div",I,[s("p",V,c(l.description),1)])):(t(),a("p",j,"暫無品牌描述"))])]))),128))])):(t(),a("div",w,o[1]||(o[1]=[s("p",{class:"text-2xl text-gray-600"},"暫無品牌資訊",-1)])))]))}},E=p(F,[["__scopeId","data-v-2e5beb4e"]]);export{E as default};
