import{r as D,U as we,V as Se,P as Ne,O as $e,o as ye,Q as be,W as V,X as Ve,c as Fe}from"./index-ad117935.js";function pe(t){return t!==null&&typeof t=="object"&&"constructor"in t&&t.constructor===Object}function fe(t,e){t===void 0&&(t={}),e===void 0&&(e={}),Object.keys(e).forEach(i=>{typeof t[i]>"u"?t[i]=e[i]:pe(e[i])&&pe(t[i])&&Object.keys(e[i]).length>0&&fe(t[i],e[i])})}const xe={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function k(){const t=typeof document<"u"?document:{};return fe(t,xe),t}const ke={document:xe,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(t){return typeof setTimeout>"u"?(t(),null):setTimeout(t,0)},cancelAnimationFrame(t){typeof setTimeout>"u"||clearTimeout(t)}};function G(){const t=typeof window<"u"?window:{};return fe(t,ke),t}function Re(t){const e=t;Object.keys(e).forEach(i=>{try{e[i]=null}catch{}try{delete e[i]}catch{}})}function le(t,e){return e===void 0&&(e=0),setTimeout(t,e)}function Z(){return Date.now()}function He(t){const e=G();let i;return e.getComputedStyle&&(i=e.getComputedStyle(t,null)),!i&&t.currentStyle&&(i=t.currentStyle),i||(i=t.style),i}function je(t,e){e===void 0&&(e="x");const i=G();let n,a,s;const o=He(t);return i.WebKitCSSMatrix?(a=o.transform||o.webkitTransform,a.split(",").length>6&&(a=a.split(", ").map(l=>l.replace(",",".")).join(", ")),s=new i.WebKitCSSMatrix(a==="none"?"":a)):(s=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),n=s.toString().split(",")),e==="x"&&(i.WebKitCSSMatrix?a=s.m41:n.length===16?a=parseFloat(n[12]):a=parseFloat(n[4])),e==="y"&&(i.WebKitCSSMatrix?a=s.m42:n.length===16?a=parseFloat(n[13]):a=parseFloat(n[5])),a||0}function U(t){return typeof t=="object"&&t!==null&&t.constructor&&Object.prototype.toString.call(t).slice(8,-1)==="Object"}function We(t){return typeof window<"u"&&typeof window.HTMLElement<"u"?t instanceof HTMLElement:t&&(t.nodeType===1||t.nodeType===11)}function _(){const t=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"];for(let i=1;i<arguments.length;i+=1){const n=i<0||arguments.length<=i?void 0:arguments[i];if(n!=null&&!We(n)){const a=Object.keys(Object(n)).filter(s=>e.indexOf(s)<0);for(let s=0,o=a.length;s<o;s+=1){const l=a[s],r=Object.getOwnPropertyDescriptor(n,l);r!==void 0&&r.enumerable&&(U(t[l])&&U(n[l])?n[l].__swiper__?t[l]=n[l]:_(t[l],n[l]):!U(t[l])&&U(n[l])?(t[l]={},n[l].__swiper__?t[l]=n[l]:_(t[l],n[l])):t[l]=n[l])}}}return t}function K(t,e,i){t.style.setProperty(e,i)}function Te(t){let{swiper:e,targetPosition:i,side:n}=t;const a=G(),s=-e.translate;let o=null,l;const r=e.params.speed;e.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(e.cssModeFrameID);const c=i>s?"next":"prev",f=(S,m)=>c==="next"&&S>=m||c==="prev"&&S<=m,p=()=>{l=new Date().getTime(),o===null&&(o=l);const S=Math.max(Math.min((l-o)/r,1),0),m=.5-Math.cos(S*Math.PI)/2;let b=s+m*(i-s);if(f(b,i)&&(b=i),e.wrapperEl.scrollTo({[n]:b}),f(b,i)){e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[n]:b})}),a.cancelAnimationFrame(e.cssModeFrameID);return}e.cssModeFrameID=a.requestAnimationFrame(p)};p()}function F(t,e){return e===void 0&&(e=""),[...t.children].filter(i=>i.matches(e))}function Ee(t,e){e===void 0&&(e=[]);const i=document.createElement(t);return i.classList.add(...Array.isArray(e)?e:[e]),i}function qe(t,e){const i=[];for(;t.previousElementSibling;){const n=t.previousElementSibling;e?n.matches(e)&&i.push(n):i.push(n),t=n}return i}function Xe(t,e){const i=[];for(;t.nextElementSibling;){const n=t.nextElementSibling;e?n.matches(e)&&i.push(n):i.push(n),t=n}return i}function j(t,e){return G().getComputedStyle(t,null).getPropertyValue(e)}function J(t){let e=t,i;if(e){for(i=0;(e=e.previousSibling)!==null;)e.nodeType===1&&(i+=1);return i}}function Ce(t,e){const i=[];let n=t.parentElement;for(;n;)e?n.matches(e)&&i.push(n):i.push(n),n=n.parentElement;return i}function oe(t,e,i){const n=G();return i?t[e==="width"?"offsetWidth":"offsetHeight"]+parseFloat(n.getComputedStyle(t,null).getPropertyValue(e==="width"?"margin-right":"margin-top"))+parseFloat(n.getComputedStyle(t,null).getPropertyValue(e==="width"?"margin-left":"margin-bottom")):t.offsetWidth}let ee;function Ye(){const t=G(),e=k();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in t||t.DocumentTouch&&e instanceof t.DocumentTouch)}}function Pe(){return ee||(ee=Ye()),ee}let te;function Ue(t){let{userAgent:e}=t===void 0?{}:t;const i=Pe(),n=G(),a=n.navigator.platform,s=e||n.navigator.userAgent,o={ios:!1,android:!1},l=n.screen.width,r=n.screen.height,c=s.match(/(Android);?[\s\/]+([\d.]+)?/);let f=s.match(/(iPad).*OS\s([\d_]+)/);const p=s.match(/(iPod)(.*OS\s([\d_]+))?/),S=!f&&s.match(/(iPhone\sOS|iOS)\s([\d_]+)/),m=a==="Win32";let b=a==="MacIntel";const h=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!f&&b&&i.touch&&h.indexOf(`${l}x${r}`)>=0&&(f=s.match(/(Version)\/([\d.]+)/),f||(f=[0,1,"13_0_0"]),b=!1),c&&!m&&(o.os="android",o.android=!0),(f||S||p)&&(o.os="ios",o.ios=!0),o}function Ke(t){return t===void 0&&(t={}),te||(te=Ue(t)),te}let ie;function Qe(){const t=G();let e=!1;function i(){const n=t.navigator.userAgent.toLowerCase();return n.indexOf("safari")>=0&&n.indexOf("chrome")<0&&n.indexOf("android")<0}if(i()){const n=String(t.navigator.userAgent);if(n.includes("Version/")){const[a,s]=n.split("Version/")[1].split(" ")[0].split(".").map(o=>Number(o));e=a<16||a===16&&s<2}}return{isSafari:e||i(),needPerspectiveFix:e,isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(t.navigator.userAgent)}}function Ze(){return ie||(ie=Qe()),ie}function Je(t){let{swiper:e,on:i,emit:n}=t;const a=G();let s=null,o=null;const l=()=>{!e||e.destroyed||!e.initialized||(n("beforeResize"),n("resize"))},r=()=>{!e||e.destroyed||!e.initialized||(s=new ResizeObserver(p=>{o=a.requestAnimationFrame(()=>{const{width:S,height:m}=e;let b=S,h=m;p.forEach(y=>{let{contentBoxSize:w,contentRect:d,target:u}=y;u&&u!==e.el||(b=d?d.width:(w[0]||w).inlineSize,h=d?d.height:(w[0]||w).blockSize)}),(b!==S||h!==m)&&l()})}),s.observe(e.el))},c=()=>{o&&a.cancelAnimationFrame(o),s&&s.unobserve&&e.el&&(s.unobserve(e.el),s=null)},f=()=>{!e||e.destroyed||!e.initialized||n("orientationchange")};i("init",()=>{if(e.params.resizeObserver&&typeof a.ResizeObserver<"u"){r();return}a.addEventListener("resize",l),a.addEventListener("orientationchange",f)}),i("destroy",()=>{c(),a.removeEventListener("resize",l),a.removeEventListener("orientationchange",f)})}function et(t){let{swiper:e,extendParams:i,on:n,emit:a}=t;const s=[],o=G(),l=function(f,p){p===void 0&&(p={});const S=o.MutationObserver||o.WebkitMutationObserver,m=new S(b=>{if(e.__preventObserver__)return;if(b.length===1){a("observerUpdate",b[0]);return}const h=function(){a("observerUpdate",b[0])};o.requestAnimationFrame?o.requestAnimationFrame(h):o.setTimeout(h,0)});m.observe(f,{attributes:typeof p.attributes>"u"?!0:p.attributes,childList:typeof p.childList>"u"?!0:p.childList,characterData:typeof p.characterData>"u"?!0:p.characterData}),s.push(m)},r=()=>{if(e.params.observer){if(e.params.observeParents){const f=Ce(e.hostEl);for(let p=0;p<f.length;p+=1)l(f[p])}l(e.hostEl,{childList:e.params.observeSlideChildren}),l(e.wrapperEl,{attributes:!1})}},c=()=>{s.forEach(f=>{f.disconnect()}),s.splice(0,s.length)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),n("init",r),n("destroy",c)}var tt={on(t,e,i){const n=this;if(!n.eventsListeners||n.destroyed||typeof e!="function")return n;const a=i?"unshift":"push";return t.split(" ").forEach(s=>{n.eventsListeners[s]||(n.eventsListeners[s]=[]),n.eventsListeners[s][a](e)}),n},once(t,e,i){const n=this;if(!n.eventsListeners||n.destroyed||typeof e!="function")return n;function a(){n.off(t,a),a.__emitterProxy&&delete a.__emitterProxy;for(var s=arguments.length,o=new Array(s),l=0;l<s;l++)o[l]=arguments[l];e.apply(n,o)}return a.__emitterProxy=e,n.on(t,a,i)},onAny(t,e){const i=this;if(!i.eventsListeners||i.destroyed||typeof t!="function")return i;const n=e?"unshift":"push";return i.eventsAnyListeners.indexOf(t)<0&&i.eventsAnyListeners[n](t),i},offAny(t){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsAnyListeners)return e;const i=e.eventsAnyListeners.indexOf(t);return i>=0&&e.eventsAnyListeners.splice(i,1),e},off(t,e){const i=this;return!i.eventsListeners||i.destroyed||!i.eventsListeners||t.split(" ").forEach(n=>{typeof e>"u"?i.eventsListeners[n]=[]:i.eventsListeners[n]&&i.eventsListeners[n].forEach((a,s)=>{(a===e||a.__emitterProxy&&a.__emitterProxy===e)&&i.eventsListeners[n].splice(s,1)})}),i},emit(){const t=this;if(!t.eventsListeners||t.destroyed||!t.eventsListeners)return t;let e,i,n;for(var a=arguments.length,s=new Array(a),o=0;o<a;o++)s[o]=arguments[o];return typeof s[0]=="string"||Array.isArray(s[0])?(e=s[0],i=s.slice(1,s.length),n=t):(e=s[0].events,i=s[0].data,n=s[0].context||t),i.unshift(n),(Array.isArray(e)?e:e.split(" ")).forEach(r=>{t.eventsAnyListeners&&t.eventsAnyListeners.length&&t.eventsAnyListeners.forEach(c=>{c.apply(n,[r,...i])}),t.eventsListeners&&t.eventsListeners[r]&&t.eventsListeners[r].forEach(c=>{c.apply(n,i)})}),t}};function it(){const t=this;let e,i;const n=t.el;typeof t.params.width<"u"&&t.params.width!==null?e=t.params.width:e=n.clientWidth,typeof t.params.height<"u"&&t.params.height!==null?i=t.params.height:i=n.clientHeight,!(e===0&&t.isHorizontal()||i===0&&t.isVertical())&&(e=e-parseInt(j(n,"padding-left")||0,10)-parseInt(j(n,"padding-right")||0,10),i=i-parseInt(j(n,"padding-top")||0,10)-parseInt(j(n,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(i)&&(i=0),Object.assign(t,{width:e,height:i,size:t.isHorizontal()?e:i}))}function nt(){const t=this;function e(T){return t.isHorizontal()?T:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[T]}function i(T,C){return parseFloat(T.getPropertyValue(e(C))||0)}const n=t.params,{wrapperEl:a,slidesEl:s,size:o,rtlTranslate:l,wrongRTL:r}=t,c=t.virtual&&n.virtual.enabled,f=c?t.virtual.slides.length:t.slides.length,p=F(s,`.${t.params.slideClass}, swiper-slide`),S=c?t.virtual.slides.length:p.length;let m=[];const b=[],h=[];let y=n.slidesOffsetBefore;typeof y=="function"&&(y=n.slidesOffsetBefore.call(t));let w=n.slidesOffsetAfter;typeof w=="function"&&(w=n.slidesOffsetAfter.call(t));const d=t.snapGrid.length,u=t.slidesGrid.length;let v=n.spaceBetween,x=-y,L=0,E=0;if(typeof o>"u")return;typeof v=="string"&&v.indexOf("%")>=0?v=parseFloat(v.replace("%",""))/100*o:typeof v=="string"&&(v=parseFloat(v)),t.virtualSize=-v,p.forEach(T=>{l?T.style.marginLeft="":T.style.marginRight="",T.style.marginBottom="",T.style.marginTop=""}),n.centeredSlides&&n.cssMode&&(K(a,"--swiper-centered-offset-before",""),K(a,"--swiper-centered-offset-after",""));const M=n.grid&&n.grid.rows>1&&t.grid;M&&t.grid.initSlides(S);let g;const O=n.slidesPerView==="auto"&&n.breakpoints&&Object.keys(n.breakpoints).filter(T=>typeof n.breakpoints[T].slidesPerView<"u").length>0;for(let T=0;T<S;T+=1){g=0;let C;if(p[T]&&(C=p[T]),M&&t.grid.updateSlide(T,C,S,e),!(p[T]&&j(C,"display")==="none")){if(n.slidesPerView==="auto"){O&&(p[T].style[e("width")]="");const P=getComputedStyle(C),I=C.style.transform,A=C.style.webkitTransform;if(I&&(C.style.transform="none"),A&&(C.style.webkitTransform="none"),n.roundLengths)g=t.isHorizontal()?oe(C,"width",!0):oe(C,"height",!0);else{const N=i(P,"width"),z=i(P,"padding-left"),$=i(P,"padding-right"),B=i(P,"margin-left"),H=i(P,"margin-right"),X=P.getPropertyValue("box-sizing");if(X&&X==="border-box")g=N+B+H;else{const{clientWidth:_e,offsetWidth:Ge}=C;g=N+z+$+B+H+(Ge-_e)}}I&&(C.style.transform=I),A&&(C.style.webkitTransform=A),n.roundLengths&&(g=Math.floor(g))}else g=(o-(n.slidesPerView-1)*v)/n.slidesPerView,n.roundLengths&&(g=Math.floor(g)),p[T]&&(p[T].style[e("width")]=`${g}px`);p[T]&&(p[T].swiperSlideSize=g),h.push(g),n.centeredSlides?(x=x+g/2+L/2+v,L===0&&T!==0&&(x=x-o/2-v),T===0&&(x=x-o/2-v),Math.abs(x)<1/1e3&&(x=0),n.roundLengths&&(x=Math.floor(x)),E%n.slidesPerGroup===0&&m.push(x),b.push(x)):(n.roundLengths&&(x=Math.floor(x)),(E-Math.min(t.params.slidesPerGroupSkip,E))%t.params.slidesPerGroup===0&&m.push(x),b.push(x),x=x+g+v),t.virtualSize+=g+v,L=g,E+=1}}if(t.virtualSize=Math.max(t.virtualSize,o)+w,l&&r&&(n.effect==="slide"||n.effect==="coverflow")&&(a.style.width=`${t.virtualSize+v}px`),n.setWrapperSize&&(a.style[e("width")]=`${t.virtualSize+v}px`),M&&t.grid.updateWrapperSize(g,m,e),!n.centeredSlides){const T=[];for(let C=0;C<m.length;C+=1){let P=m[C];n.roundLengths&&(P=Math.floor(P)),m[C]<=t.virtualSize-o&&T.push(P)}m=T,Math.floor(t.virtualSize-o)-Math.floor(m[m.length-1])>1&&m.push(t.virtualSize-o)}if(c&&n.loop){const T=h[0]+v;if(n.slidesPerGroup>1){const C=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/n.slidesPerGroup),P=T*n.slidesPerGroup;for(let I=0;I<C;I+=1)m.push(m[m.length-1]+P)}for(let C=0;C<t.virtual.slidesBefore+t.virtual.slidesAfter;C+=1)n.slidesPerGroup===1&&m.push(m[m.length-1]+T),b.push(b[b.length-1]+T),t.virtualSize+=T}if(m.length===0&&(m=[0]),v!==0){const T=t.isHorizontal()&&l?"marginLeft":e("marginRight");p.filter((C,P)=>!n.cssMode||n.loop?!0:P!==p.length-1).forEach(C=>{C.style[T]=`${v}px`})}if(n.centeredSlides&&n.centeredSlidesBounds){let T=0;h.forEach(P=>{T+=P+(v||0)}),T-=v;const C=T-o;m=m.map(P=>P<=0?-y:P>C?C+w:P)}if(n.centerInsufficientSlides){let T=0;if(h.forEach(C=>{T+=C+(v||0)}),T-=v,T<o){const C=(o-T)/2;m.forEach((P,I)=>{m[I]=P-C}),b.forEach((P,I)=>{b[I]=P+C})}}if(Object.assign(t,{slides:p,snapGrid:m,slidesGrid:b,slidesSizesGrid:h}),n.centeredSlides&&n.cssMode&&!n.centeredSlidesBounds){K(a,"--swiper-centered-offset-before",`${-m[0]}px`),K(a,"--swiper-centered-offset-after",`${t.size/2-h[h.length-1]/2}px`);const T=-t.snapGrid[0],C=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(P=>P+T),t.slidesGrid=t.slidesGrid.map(P=>P+C)}if(S!==f&&t.emit("slidesLengthChange"),m.length!==d&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),b.length!==u&&t.emit("slidesGridLengthChange"),n.watchSlidesProgress&&t.updateSlidesOffset(),!c&&!n.cssMode&&(n.effect==="slide"||n.effect==="fade")){const T=`${n.containerModifierClass}backface-hidden`,C=t.el.classList.contains(T);S<=n.maxBackfaceHiddenSlides?C||t.el.classList.add(T):C&&t.el.classList.remove(T)}}function st(t){const e=this,i=[],n=e.virtual&&e.params.virtual.enabled;let a=0,s;typeof t=="number"?e.setTransition(t):t===!0&&e.setTransition(e.params.speed);const o=l=>n?e.slides[e.getSlideIndexByData(l)]:e.slides[l];if(e.params.slidesPerView!=="auto"&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach(l=>{i.push(l)});else for(s=0;s<Math.ceil(e.params.slidesPerView);s+=1){const l=e.activeIndex+s;if(l>e.slides.length&&!n)break;i.push(o(l))}else i.push(o(e.activeIndex));for(s=0;s<i.length;s+=1)if(typeof i[s]<"u"){const l=i[s].offsetHeight;a=l>a?l:a}(a||a===0)&&(e.wrapperEl.style.height=`${a}px`)}function at(){const t=this,e=t.slides,i=t.isElement?t.isHorizontal()?t.wrapperEl.offsetLeft:t.wrapperEl.offsetTop:0;for(let n=0;n<e.length;n+=1)e[n].swiperSlideOffset=(t.isHorizontal()?e[n].offsetLeft:e[n].offsetTop)-i-t.cssOverflowAdjustment()}function rt(t){t===void 0&&(t=this&&this.translate||0);const e=this,i=e.params,{slides:n,rtlTranslate:a,snapGrid:s}=e;if(n.length===0)return;typeof n[0].swiperSlideOffset>"u"&&e.updateSlidesOffset();let o=-t;a&&(o=t),n.forEach(r=>{r.classList.remove(i.slideVisibleClass)}),e.visibleSlidesIndexes=[],e.visibleSlides=[];let l=i.spaceBetween;typeof l=="string"&&l.indexOf("%")>=0?l=parseFloat(l.replace("%",""))/100*e.size:typeof l=="string"&&(l=parseFloat(l));for(let r=0;r<n.length;r+=1){const c=n[r];let f=c.swiperSlideOffset;i.cssMode&&i.centeredSlides&&(f-=n[0].swiperSlideOffset);const p=(o+(i.centeredSlides?e.minTranslate():0)-f)/(c.swiperSlideSize+l),S=(o-s[0]+(i.centeredSlides?e.minTranslate():0)-f)/(c.swiperSlideSize+l),m=-(o-f),b=m+e.slidesSizesGrid[r];(m>=0&&m<e.size-1||b>1&&b<=e.size||m<=0&&b>=e.size)&&(e.visibleSlides.push(c),e.visibleSlidesIndexes.push(r),n[r].classList.add(i.slideVisibleClass)),c.progress=a?-p:p,c.originalProgress=a?-S:S}}function lt(t){const e=this;if(typeof t>"u"){const f=e.rtlTranslate?-1:1;t=e&&e.translate&&e.translate*f||0}const i=e.params,n=e.maxTranslate()-e.minTranslate();let{progress:a,isBeginning:s,isEnd:o,progressLoop:l}=e;const r=s,c=o;if(n===0)a=0,s=!0,o=!0;else{a=(t-e.minTranslate())/n;const f=Math.abs(t-e.minTranslate())<1,p=Math.abs(t-e.maxTranslate())<1;s=f||a<=0,o=p||a>=1,f&&(a=0),p&&(a=1)}if(i.loop){const f=e.getSlideIndexByData(0),p=e.getSlideIndexByData(e.slides.length-1),S=e.slidesGrid[f],m=e.slidesGrid[p],b=e.slidesGrid[e.slidesGrid.length-1],h=Math.abs(t);h>=S?l=(h-S)/b:l=(h+b-m)/b,l>1&&(l-=1)}Object.assign(e,{progress:a,progressLoop:l,isBeginning:s,isEnd:o}),(i.watchSlidesProgress||i.centeredSlides&&i.autoHeight)&&e.updateSlidesProgress(t),s&&!r&&e.emit("reachBeginning toEdge"),o&&!c&&e.emit("reachEnd toEdge"),(r&&!s||c&&!o)&&e.emit("fromEdge"),e.emit("progress",a)}function ot(){const t=this,{slides:e,params:i,slidesEl:n,activeIndex:a}=t,s=t.virtual&&i.virtual.enabled,o=r=>F(n,`.${i.slideClass}${r}, swiper-slide${r}`)[0];e.forEach(r=>{r.classList.remove(i.slideActiveClass,i.slideNextClass,i.slidePrevClass)});let l;if(s)if(i.loop){let r=a-t.virtual.slidesBefore;r<0&&(r=t.virtual.slides.length+r),r>=t.virtual.slides.length&&(r-=t.virtual.slides.length),l=o(`[data-swiper-slide-index="${r}"]`)}else l=o(`[data-swiper-slide-index="${a}"]`);else l=e[a];if(l){l.classList.add(i.slideActiveClass);let r=Xe(l,`.${i.slideClass}, swiper-slide`)[0];i.loop&&!r&&(r=e[0]),r&&r.classList.add(i.slideNextClass);let c=qe(l,`.${i.slideClass}, swiper-slide`)[0];i.loop&&!c===0&&(c=e[e.length-1]),c&&c.classList.add(i.slidePrevClass)}t.emitSlidesClasses()}const Q=(t,e)=>{if(!t||t.destroyed||!t.params)return;const i=()=>t.isElement?"swiper-slide":`.${t.params.slideClass}`,n=e.closest(i());if(n){let a=n.querySelector(`.${t.params.lazyPreloaderClass}`);!a&&t.isElement&&(n.shadowRoot?a=n.shadowRoot.querySelector(`.${t.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{n.shadowRoot&&(a=n.shadowRoot.querySelector(`.${t.params.lazyPreloaderClass}`),a&&a.remove())})),a&&a.remove()}},ne=(t,e)=>{if(!t.slides[e])return;const i=t.slides[e].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},de=t=>{if(!t||t.destroyed||!t.params)return;let e=t.params.lazyPreloadPrevNext;const i=t.slides.length;if(!i||!e||e<0)return;e=Math.min(e,i);const n=t.params.slidesPerView==="auto"?t.slidesPerViewDynamic():Math.ceil(t.params.slidesPerView),a=t.activeIndex;if(t.params.grid&&t.params.grid.rows>1){const o=a,l=[o-e];l.push(...Array.from({length:e}).map((r,c)=>o+n+c)),t.slides.forEach((r,c)=>{l.includes(r.column)&&ne(t,c)});return}const s=a+n-1;if(t.params.rewind||t.params.loop)for(let o=a-e;o<=s+e;o+=1){const l=(o%i+i)%i;(l<a||l>s)&&ne(t,l)}else for(let o=Math.max(a-e,0);o<=Math.min(s+e,i-1);o+=1)o!==a&&(o>s||o<a)&&ne(t,o)};function dt(t){const{slidesGrid:e,params:i}=t,n=t.rtlTranslate?t.translate:-t.translate;let a;for(let s=0;s<e.length;s+=1)typeof e[s+1]<"u"?n>=e[s]&&n<e[s+1]-(e[s+1]-e[s])/2?a=s:n>=e[s]&&n<e[s+1]&&(a=s+1):n>=e[s]&&(a=s);return i.normalizeSlideIndex&&(a<0||typeof a>"u")&&(a=0),a}function ut(t){const e=this,i=e.rtlTranslate?e.translate:-e.translate,{snapGrid:n,params:a,activeIndex:s,realIndex:o,snapIndex:l}=e;let r=t,c;const f=S=>{let m=S-e.virtual.slidesBefore;return m<0&&(m=e.virtual.slides.length+m),m>=e.virtual.slides.length&&(m-=e.virtual.slides.length),m};if(typeof r>"u"&&(r=dt(e)),n.indexOf(i)>=0)c=n.indexOf(i);else{const S=Math.min(a.slidesPerGroupSkip,r);c=S+Math.floor((r-S)/a.slidesPerGroup)}if(c>=n.length&&(c=n.length-1),r===s){c!==l&&(e.snapIndex=c,e.emit("snapIndexChange")),e.params.loop&&e.virtual&&e.params.virtual.enabled&&(e.realIndex=f(r));return}let p;e.virtual&&a.virtual.enabled&&a.loop?p=f(r):e.slides[r]?p=parseInt(e.slides[r].getAttribute("data-swiper-slide-index")||r,10):p=r,Object.assign(e,{previousSnapIndex:l,snapIndex:c,previousRealIndex:o,realIndex:p,previousIndex:s,activeIndex:r}),e.initialized&&de(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&(o!==p&&e.emit("realIndexChange"),e.emit("slideChange"))}function ft(t,e){const i=this,n=i.params;let a=t.closest(`.${n.slideClass}, swiper-slide`);!a&&i.isElement&&e&&e.length>1&&e.includes(t)&&[...e.slice(e.indexOf(t)+1,e.length)].forEach(l=>{!a&&l.matches&&l.matches(`.${n.slideClass}, swiper-slide`)&&(a=l)});let s=!1,o;if(a){for(let l=0;l<i.slides.length;l+=1)if(i.slides[l]===a){s=!0,o=l;break}}if(a&&s)i.clickedSlide=a,i.virtual&&i.params.virtual.enabled?i.clickedIndex=parseInt(a.getAttribute("data-swiper-slide-index"),10):i.clickedIndex=o;else{i.clickedSlide=void 0,i.clickedIndex=void 0;return}n.slideToClickedSlide&&i.clickedIndex!==void 0&&i.clickedIndex!==i.activeIndex&&i.slideToClickedSlide()}var ct={updateSize:it,updateSlides:nt,updateAutoHeight:st,updateSlidesOffset:at,updateSlidesProgress:rt,updateProgress:lt,updateSlidesClasses:ot,updateActiveIndex:ut,updateClickedSlide:ft};function pt(t){t===void 0&&(t=this.isHorizontal()?"x":"y");const e=this,{params:i,rtlTranslate:n,translate:a,wrapperEl:s}=e;if(i.virtualTranslate)return n?-a:a;if(i.cssMode)return a;let o=je(s,t);return o+=e.cssOverflowAdjustment(),n&&(o=-o),o||0}function mt(t,e){const i=this,{rtlTranslate:n,params:a,wrapperEl:s,progress:o}=i;let l=0,r=0;const c=0;i.isHorizontal()?l=n?-t:t:r=t,a.roundLengths&&(l=Math.floor(l),r=Math.floor(r)),i.previousTranslate=i.translate,i.translate=i.isHorizontal()?l:r,a.cssMode?s[i.isHorizontal()?"scrollLeft":"scrollTop"]=i.isHorizontal()?-l:-r:a.virtualTranslate||(i.isHorizontal()?l-=i.cssOverflowAdjustment():r-=i.cssOverflowAdjustment(),s.style.transform=`translate3d(${l}px, ${r}px, ${c}px)`);let f;const p=i.maxTranslate()-i.minTranslate();p===0?f=0:f=(t-i.minTranslate())/p,f!==o&&i.updateProgress(t),i.emit("setTranslate",i.translate,e)}function ht(){return-this.snapGrid[0]}function gt(){return-this.snapGrid[this.snapGrid.length-1]}function vt(t,e,i,n,a){t===void 0&&(t=0),e===void 0&&(e=this.params.speed),i===void 0&&(i=!0),n===void 0&&(n=!0);const s=this,{params:o,wrapperEl:l}=s;if(s.animating&&o.preventInteractionOnTransition)return!1;const r=s.minTranslate(),c=s.maxTranslate();let f;if(n&&t>r?f=r:n&&t<c?f=c:f=t,s.updateProgress(f),o.cssMode){const p=s.isHorizontal();if(e===0)l[p?"scrollLeft":"scrollTop"]=-f;else{if(!s.support.smoothScroll)return Te({swiper:s,targetPosition:-f,side:p?"left":"top"}),!0;l.scrollTo({[p?"left":"top"]:-f,behavior:"smooth"})}return!0}return e===0?(s.setTransition(0),s.setTranslate(f),i&&(s.emit("beforeTransitionStart",e,a),s.emit("transitionEnd"))):(s.setTransition(e),s.setTranslate(f),i&&(s.emit("beforeTransitionStart",e,a),s.emit("transitionStart")),s.animating||(s.animating=!0,s.onTranslateToWrapperTransitionEnd||(s.onTranslateToWrapperTransitionEnd=function(S){!s||s.destroyed||S.target===this&&(s.wrapperEl.removeEventListener("transitionend",s.onTranslateToWrapperTransitionEnd),s.onTranslateToWrapperTransitionEnd=null,delete s.onTranslateToWrapperTransitionEnd,i&&s.emit("transitionEnd"))}),s.wrapperEl.addEventListener("transitionend",s.onTranslateToWrapperTransitionEnd))),!0}var wt={getTranslate:pt,setTranslate:mt,minTranslate:ht,maxTranslate:gt,translateTo:vt};function St(t,e){const i=this;i.params.cssMode||(i.wrapperEl.style.transitionDuration=`${t}ms`,i.wrapperEl.style.transitionDelay=t===0?"0ms":""),i.emit("setTransition",t,e)}function Me(t){let{swiper:e,runCallbacks:i,direction:n,step:a}=t;const{activeIndex:s,previousIndex:o}=e;let l=n;if(l||(s>o?l="next":s<o?l="prev":l="reset"),e.emit(`transition${a}`),i&&s!==o){if(l==="reset"){e.emit(`slideResetTransition${a}`);return}e.emit(`slideChangeTransition${a}`),l==="next"?e.emit(`slideNextTransition${a}`):e.emit(`slidePrevTransition${a}`)}}function yt(t,e){t===void 0&&(t=!0);const i=this,{params:n}=i;n.cssMode||(n.autoHeight&&i.updateAutoHeight(),Me({swiper:i,runCallbacks:t,direction:e,step:"Start"}))}function bt(t,e){t===void 0&&(t=!0);const i=this,{params:n}=i;i.animating=!1,!n.cssMode&&(i.setTransition(0),Me({swiper:i,runCallbacks:t,direction:e,step:"End"}))}var xt={setTransition:St,transitionStart:yt,transitionEnd:bt};function Tt(t,e,i,n,a){t===void 0&&(t=0),e===void 0&&(e=this.params.speed),i===void 0&&(i=!0),typeof t=="string"&&(t=parseInt(t,10));const s=this;let o=t;o<0&&(o=0);const{params:l,snapGrid:r,slidesGrid:c,previousIndex:f,activeIndex:p,rtlTranslate:S,wrapperEl:m,enabled:b}=s;if(s.animating&&l.preventInteractionOnTransition||!b&&!n&&!a)return!1;const h=Math.min(s.params.slidesPerGroupSkip,o);let y=h+Math.floor((o-h)/s.params.slidesPerGroup);y>=r.length&&(y=r.length-1);const w=-r[y];if(l.normalizeSlideIndex)for(let u=0;u<c.length;u+=1){const v=-Math.floor(w*100),x=Math.floor(c[u]*100),L=Math.floor(c[u+1]*100);typeof c[u+1]<"u"?v>=x&&v<L-(L-x)/2?o=u:v>=x&&v<L&&(o=u+1):v>=x&&(o=u)}if(s.initialized&&o!==p&&(!s.allowSlideNext&&(S?w>s.translate&&w>s.minTranslate():w<s.translate&&w<s.minTranslate())||!s.allowSlidePrev&&w>s.translate&&w>s.maxTranslate()&&(p||0)!==o))return!1;o!==(f||0)&&i&&s.emit("beforeSlideChangeStart"),s.updateProgress(w);let d;if(o>p?d="next":o<p?d="prev":d="reset",S&&-w===s.translate||!S&&w===s.translate)return s.updateActiveIndex(o),l.autoHeight&&s.updateAutoHeight(),s.updateSlidesClasses(),l.effect!=="slide"&&s.setTranslate(w),d!=="reset"&&(s.transitionStart(i,d),s.transitionEnd(i,d)),!1;if(l.cssMode){const u=s.isHorizontal(),v=S?w:-w;if(e===0){const x=s.virtual&&s.params.virtual.enabled;x&&(s.wrapperEl.style.scrollSnapType="none",s._immediateVirtual=!0),x&&!s._cssModeVirtualInitialSet&&s.params.initialSlide>0?(s._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{m[u?"scrollLeft":"scrollTop"]=v})):m[u?"scrollLeft":"scrollTop"]=v,x&&requestAnimationFrame(()=>{s.wrapperEl.style.scrollSnapType="",s._immediateVirtual=!1})}else{if(!s.support.smoothScroll)return Te({swiper:s,targetPosition:v,side:u?"left":"top"}),!0;m.scrollTo({[u?"left":"top"]:v,behavior:"smooth"})}return!0}return s.setTransition(e),s.setTranslate(w),s.updateActiveIndex(o),s.updateSlidesClasses(),s.emit("beforeTransitionStart",e,n),s.transitionStart(i,d),e===0?s.transitionEnd(i,d):s.animating||(s.animating=!0,s.onSlideToWrapperTransitionEnd||(s.onSlideToWrapperTransitionEnd=function(v){!s||s.destroyed||v.target===this&&(s.wrapperEl.removeEventListener("transitionend",s.onSlideToWrapperTransitionEnd),s.onSlideToWrapperTransitionEnd=null,delete s.onSlideToWrapperTransitionEnd,s.transitionEnd(i,d))}),s.wrapperEl.addEventListener("transitionend",s.onSlideToWrapperTransitionEnd)),!0}function Et(t,e,i,n){t===void 0&&(t=0),e===void 0&&(e=this.params.speed),i===void 0&&(i=!0),typeof t=="string"&&(t=parseInt(t,10));const a=this;let s=t;return a.params.loop&&(a.virtual&&a.params.virtual.enabled?s=s+a.virtual.slidesBefore:s=a.getSlideIndexByData(s)),a.slideTo(s,e,i,n)}function Ct(t,e,i){t===void 0&&(t=this.params.speed),e===void 0&&(e=!0);const n=this,{enabled:a,params:s,animating:o}=n;if(!a)return n;let l=s.slidesPerGroup;s.slidesPerView==="auto"&&s.slidesPerGroup===1&&s.slidesPerGroupAuto&&(l=Math.max(n.slidesPerViewDynamic("current",!0),1));const r=n.activeIndex<s.slidesPerGroupSkip?1:l,c=n.virtual&&s.virtual.enabled;if(s.loop){if(o&&!c&&s.loopPreventsSliding)return!1;if(n.loopFix({direction:"next"}),n._clientLeft=n.wrapperEl.clientLeft,n.activeIndex===n.slides.length-1&&s.cssMode)return requestAnimationFrame(()=>{n.slideTo(n.activeIndex+r,t,e,i)}),!0}return s.rewind&&n.isEnd?n.slideTo(0,t,e,i):n.slideTo(n.activeIndex+r,t,e,i)}function Pt(t,e,i){t===void 0&&(t=this.params.speed),e===void 0&&(e=!0);const n=this,{params:a,snapGrid:s,slidesGrid:o,rtlTranslate:l,enabled:r,animating:c}=n;if(!r)return n;const f=n.virtual&&a.virtual.enabled;if(a.loop){if(c&&!f&&a.loopPreventsSliding)return!1;n.loopFix({direction:"prev"}),n._clientLeft=n.wrapperEl.clientLeft}const p=l?n.translate:-n.translate;function S(w){return w<0?-Math.floor(Math.abs(w)):Math.floor(w)}const m=S(p),b=s.map(w=>S(w));let h=s[b.indexOf(m)-1];if(typeof h>"u"&&a.cssMode){let w;s.forEach((d,u)=>{m>=d&&(w=u)}),typeof w<"u"&&(h=s[w>0?w-1:w])}let y=0;if(typeof h<"u"&&(y=o.indexOf(h),y<0&&(y=n.activeIndex-1),a.slidesPerView==="auto"&&a.slidesPerGroup===1&&a.slidesPerGroupAuto&&(y=y-n.slidesPerViewDynamic("previous",!0)+1,y=Math.max(y,0))),a.rewind&&n.isBeginning){const w=n.params.virtual&&n.params.virtual.enabled&&n.virtual?n.virtual.slides.length-1:n.slides.length-1;return n.slideTo(w,t,e,i)}else if(a.loop&&n.activeIndex===0&&a.cssMode)return requestAnimationFrame(()=>{n.slideTo(y,t,e,i)}),!0;return n.slideTo(y,t,e,i)}function Mt(t,e,i){t===void 0&&(t=this.params.speed),e===void 0&&(e=!0);const n=this;return n.slideTo(n.activeIndex,t,e,i)}function Lt(t,e,i,n){t===void 0&&(t=this.params.speed),e===void 0&&(e=!0),n===void 0&&(n=.5);const a=this;let s=a.activeIndex;const o=Math.min(a.params.slidesPerGroupSkip,s),l=o+Math.floor((s-o)/a.params.slidesPerGroup),r=a.rtlTranslate?a.translate:-a.translate;if(r>=a.snapGrid[l]){const c=a.snapGrid[l],f=a.snapGrid[l+1];r-c>(f-c)*n&&(s+=a.params.slidesPerGroup)}else{const c=a.snapGrid[l-1],f=a.snapGrid[l];r-c<=(f-c)*n&&(s-=a.params.slidesPerGroup)}return s=Math.max(s,0),s=Math.min(s,a.slidesGrid.length-1),a.slideTo(s,t,e,i)}function Ot(){const t=this,{params:e,slidesEl:i}=t,n=e.slidesPerView==="auto"?t.slidesPerViewDynamic():e.slidesPerView;let a=t.clickedIndex,s;const o=t.isElement?"swiper-slide":`.${e.slideClass}`;if(e.loop){if(t.animating)return;s=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?a<t.loopedSlides-n/2||a>t.slides.length-t.loopedSlides+n/2?(t.loopFix(),a=t.getSlideIndex(F(i,`${o}[data-swiper-slide-index="${s}"]`)[0]),le(()=>{t.slideTo(a)})):t.slideTo(a):a>t.slides.length-n?(t.loopFix(),a=t.getSlideIndex(F(i,`${o}[data-swiper-slide-index="${s}"]`)[0]),le(()=>{t.slideTo(a)})):t.slideTo(a)}else t.slideTo(a)}var It={slideTo:Tt,slideToLoop:Et,slideNext:Ct,slidePrev:Pt,slideReset:Mt,slideToClosest:Lt,slideToClickedSlide:Ot};function zt(t){const e=this,{params:i,slidesEl:n}=e;if(!i.loop||e.virtual&&e.params.virtual.enabled)return;F(n,`.${i.slideClass}, swiper-slide`).forEach((s,o)=>{s.setAttribute("data-swiper-slide-index",o)}),e.loopFix({slideRealIndex:t,direction:i.centeredSlides?void 0:"next"})}function At(t){let{slideRealIndex:e,slideTo:i=!0,direction:n,setTranslate:a,activeSlideIndex:s,byController:o,byMousewheel:l}=t===void 0?{}:t;const r=this;if(!r.params.loop)return;r.emit("beforeLoopFix");const{slides:c,allowSlidePrev:f,allowSlideNext:p,slidesEl:S,params:m}=r;if(r.allowSlidePrev=!0,r.allowSlideNext=!0,r.virtual&&m.virtual.enabled){i&&(!m.centeredSlides&&r.snapIndex===0?r.slideTo(r.virtual.slides.length,0,!1,!0):m.centeredSlides&&r.snapIndex<m.slidesPerView?r.slideTo(r.virtual.slides.length+r.snapIndex,0,!1,!0):r.snapIndex===r.snapGrid.length-1&&r.slideTo(r.virtual.slidesBefore,0,!1,!0)),r.allowSlidePrev=f,r.allowSlideNext=p,r.emit("loopFix");return}const b=m.slidesPerView==="auto"?r.slidesPerViewDynamic():Math.ceil(parseFloat(m.slidesPerView,10));let h=m.loopedSlides||b;h%m.slidesPerGroup!==0&&(h+=m.slidesPerGroup-h%m.slidesPerGroup),r.loopedSlides=h;const y=[],w=[];let d=r.activeIndex;typeof s>"u"?s=r.getSlideIndex(r.slides.filter(E=>E.classList.contains(m.slideActiveClass))[0]):d=s;const u=n==="next"||!n,v=n==="prev"||!n;let x=0,L=0;if(s<h){x=Math.max(h-s,m.slidesPerGroup);for(let E=0;E<h-s;E+=1){const M=E-Math.floor(E/c.length)*c.length;y.push(c.length-M-1)}}else if(s>r.slides.length-h*2){L=Math.max(s-(r.slides.length-h*2),m.slidesPerGroup);for(let E=0;E<L;E+=1){const M=E-Math.floor(E/c.length)*c.length;w.push(M)}}if(v&&y.forEach(E=>{r.slides[E].swiperLoopMoveDOM=!0,S.prepend(r.slides[E]),r.slides[E].swiperLoopMoveDOM=!1}),u&&w.forEach(E=>{r.slides[E].swiperLoopMoveDOM=!0,S.append(r.slides[E]),r.slides[E].swiperLoopMoveDOM=!1}),r.recalcSlides(),m.slidesPerView==="auto"&&r.updateSlides(),m.watchSlidesProgress&&r.updateSlidesOffset(),i){if(y.length>0&&v)if(typeof e>"u"){const E=r.slidesGrid[d],g=r.slidesGrid[d+x]-E;l?r.setTranslate(r.translate-g):(r.slideTo(d+x,0,!1,!0),a&&(r.touches[r.isHorizontal()?"startX":"startY"]+=g,r.touchEventsData.currentTranslate=r.translate))}else a&&(r.slideToLoop(e,0,!1,!0),r.touchEventsData.currentTranslate=r.translate);else if(w.length>0&&u)if(typeof e>"u"){const E=r.slidesGrid[d],g=r.slidesGrid[d-L]-E;l?r.setTranslate(r.translate-g):(r.slideTo(d-L,0,!1,!0),a&&(r.touches[r.isHorizontal()?"startX":"startY"]+=g,r.touchEventsData.currentTranslate=r.translate))}else r.slideToLoop(e,0,!1,!0)}if(r.allowSlidePrev=f,r.allowSlideNext=p,r.controller&&r.controller.control&&!o){const E={slideRealIndex:e,direction:n,setTranslate:a,activeSlideIndex:s,byController:!0};Array.isArray(r.controller.control)?r.controller.control.forEach(M=>{!M.destroyed&&M.params.loop&&M.loopFix({...E,slideTo:M.params.slidesPerView===m.slidesPerView?i:!1})}):r.controller.control instanceof r.constructor&&r.controller.control.params.loop&&r.controller.control.loopFix({...E,slideTo:r.controller.control.params.slidesPerView===m.slidesPerView?i:!1})}r.emit("loopFix")}function Bt(){const t=this,{params:e,slidesEl:i}=t;if(!e.loop||t.virtual&&t.params.virtual.enabled)return;t.recalcSlides();const n=[];t.slides.forEach(a=>{const s=typeof a.swiperSlideIndex>"u"?a.getAttribute("data-swiper-slide-index")*1:a.swiperSlideIndex;n[s]=a}),t.slides.forEach(a=>{a.removeAttribute("data-swiper-slide-index")}),n.forEach(a=>{i.append(a)}),t.recalcSlides(),t.slideTo(t.realIndex,0)}var Dt={loopCreate:zt,loopFix:At,loopDestroy:Bt};function _t(t){const e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;const i=e.params.touchEventsTarget==="container"?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),i.style.cursor="move",i.style.cursor=t?"grabbing":"grab",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1})}function Gt(){const t=this;t.params.watchOverflow&&t.isLocked||t.params.cssMode||(t.isElement&&(t.__preventObserver__=!0),t[t.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1}))}var Nt={setGrabCursor:_t,unsetGrabCursor:Gt};function $t(t,e){e===void 0&&(e=this);function i(n){if(!n||n===k()||n===G())return null;n.assignedSlot&&(n=n.assignedSlot);const a=n.closest(t);return!a&&!n.getRootNode?null:a||i(n.getRootNode().host)}return i(e)}function Vt(t){const e=this,i=k(),n=G(),a=e.touchEventsData;a.evCache.push(t);const{params:s,touches:o,enabled:l}=e;if(!l||!s.simulateTouch&&t.pointerType==="mouse"||e.animating&&s.preventInteractionOnTransition)return;!e.animating&&s.cssMode&&s.loop&&e.loopFix();let r=t;r.originalEvent&&(r=r.originalEvent);let c=r.target;if(s.touchEventsTarget==="wrapper"&&!e.wrapperEl.contains(c)||"which"in r&&r.which===3||"button"in r&&r.button>0||a.isTouched&&a.isMoved)return;const f=!!s.noSwipingClass&&s.noSwipingClass!=="",p=t.composedPath?t.composedPath():t.path;f&&r.target&&r.target.shadowRoot&&p&&(c=p[0]);const S=s.noSwipingSelector?s.noSwipingSelector:`.${s.noSwipingClass}`,m=!!(r.target&&r.target.shadowRoot);if(s.noSwiping&&(m?$t(S,c):c.closest(S))){e.allowClick=!0;return}if(s.swipeHandler&&!c.closest(s.swipeHandler))return;o.currentX=r.pageX,o.currentY=r.pageY;const b=o.currentX,h=o.currentY,y=s.edgeSwipeDetection||s.iOSEdgeSwipeDetection,w=s.edgeSwipeThreshold||s.iOSEdgeSwipeThreshold;if(y&&(b<=w||b>=n.innerWidth-w))if(y==="prevent")t.preventDefault();else return;Object.assign(a,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=b,o.startY=h,a.touchStartTime=Z(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,s.threshold>0&&(a.allowThresholdMove=!1);let d=!0;c.matches(a.focusableElements)&&(d=!1,c.nodeName==="SELECT"&&(a.isTouched=!1)),i.activeElement&&i.activeElement.matches(a.focusableElements)&&i.activeElement!==c&&i.activeElement.blur();const u=d&&e.allowTouchMove&&s.touchStartPreventDefault;(s.touchStartForcePreventDefault||u)&&!c.isContentEditable&&r.preventDefault(),s.freeMode&&s.freeMode.enabled&&e.freeMode&&e.animating&&!s.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",r)}function Ft(t){const e=k(),i=this,n=i.touchEventsData,{params:a,touches:s,rtlTranslate:o,enabled:l}=i;if(!l||!a.simulateTouch&&t.pointerType==="mouse")return;let r=t;if(r.originalEvent&&(r=r.originalEvent),!n.isTouched){n.startMoving&&n.isScrolling&&i.emit("touchMoveOpposite",r);return}const c=n.evCache.findIndex(E=>E.pointerId===r.pointerId);c>=0&&(n.evCache[c]=r);const f=n.evCache.length>1?n.evCache[0]:r,p=f.pageX,S=f.pageY;if(r.preventedByNestedSwiper){s.startX=p,s.startY=S;return}if(!i.allowTouchMove){r.target.matches(n.focusableElements)||(i.allowClick=!1),n.isTouched&&(Object.assign(s,{startX:p,startY:S,prevX:i.touches.currentX,prevY:i.touches.currentY,currentX:p,currentY:S}),n.touchStartTime=Z());return}if(a.touchReleaseOnEdges&&!a.loop){if(i.isVertical()){if(S<s.startY&&i.translate<=i.maxTranslate()||S>s.startY&&i.translate>=i.minTranslate()){n.isTouched=!1,n.isMoved=!1;return}}else if(p<s.startX&&i.translate<=i.maxTranslate()||p>s.startX&&i.translate>=i.minTranslate())return}if(e.activeElement&&r.target===e.activeElement&&r.target.matches(n.focusableElements)){n.isMoved=!0,i.allowClick=!1;return}if(n.allowTouchCallbacks&&i.emit("touchMove",r),r.targetTouches&&r.targetTouches.length>1)return;s.currentX=p,s.currentY=S;const m=s.currentX-s.startX,b=s.currentY-s.startY;if(i.params.threshold&&Math.sqrt(m**2+b**2)<i.params.threshold)return;if(typeof n.isScrolling>"u"){let E;i.isHorizontal()&&s.currentY===s.startY||i.isVertical()&&s.currentX===s.startX?n.isScrolling=!1:m*m+b*b>=25&&(E=Math.atan2(Math.abs(b),Math.abs(m))*180/Math.PI,n.isScrolling=i.isHorizontal()?E>a.touchAngle:90-E>a.touchAngle)}if(n.isScrolling&&i.emit("touchMoveOpposite",r),typeof n.startMoving>"u"&&(s.currentX!==s.startX||s.currentY!==s.startY)&&(n.startMoving=!0),n.isScrolling||i.zoom&&i.params.zoom&&i.params.zoom.enabled&&n.evCache.length>1){n.isTouched=!1;return}if(!n.startMoving)return;i.allowClick=!1,!a.cssMode&&r.cancelable&&r.preventDefault(),a.touchMoveStopPropagation&&!a.nested&&r.stopPropagation();let h=i.isHorizontal()?m:b,y=i.isHorizontal()?s.currentX-s.previousX:s.currentY-s.previousY;a.oneWayMovement&&(h=Math.abs(h)*(o?1:-1),y=Math.abs(y)*(o?1:-1)),s.diff=h,h*=a.touchRatio,o&&(h=-h,y=-y);const w=i.touchesDirection;i.swipeDirection=h>0?"prev":"next",i.touchesDirection=y>0?"prev":"next";const d=i.params.loop&&!a.cssMode,u=i.swipeDirection==="next"&&i.allowSlideNext||i.swipeDirection==="prev"&&i.allowSlidePrev;if(!n.isMoved){if(d&&u&&i.loopFix({direction:i.swipeDirection}),n.startTranslate=i.getTranslate(),i.setTransition(0),i.animating){const E=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});i.wrapperEl.dispatchEvent(E)}n.allowMomentumBounce=!1,a.grabCursor&&(i.allowSlideNext===!0||i.allowSlidePrev===!0)&&i.setGrabCursor(!0),i.emit("sliderFirstMove",r)}let v;n.isMoved&&w!==i.touchesDirection&&d&&u&&Math.abs(h)>=1&&(i.loopFix({direction:i.swipeDirection,setTranslate:!0}),v=!0),i.emit("sliderMove",r),n.isMoved=!0,n.currentTranslate=h+n.startTranslate;let x=!0,L=a.resistanceRatio;if(a.touchReleaseOnEdges&&(L=0),h>0?(d&&u&&!v&&n.currentTranslate>(a.centeredSlides?i.minTranslate()-i.size/2:i.minTranslate())&&i.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),n.currentTranslate>i.minTranslate()&&(x=!1,a.resistance&&(n.currentTranslate=i.minTranslate()-1+(-i.minTranslate()+n.startTranslate+h)**L))):h<0&&(d&&u&&!v&&n.currentTranslate<(a.centeredSlides?i.maxTranslate()+i.size/2:i.maxTranslate())&&i.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:i.slides.length-(a.slidesPerView==="auto"?i.slidesPerViewDynamic():Math.ceil(parseFloat(a.slidesPerView,10)))}),n.currentTranslate<i.maxTranslate()&&(x=!1,a.resistance&&(n.currentTranslate=i.maxTranslate()+1-(i.maxTranslate()-n.startTranslate-h)**L))),x&&(r.preventedByNestedSwiper=!0),!i.allowSlideNext&&i.swipeDirection==="next"&&n.currentTranslate<n.startTranslate&&(n.currentTranslate=n.startTranslate),!i.allowSlidePrev&&i.swipeDirection==="prev"&&n.currentTranslate>n.startTranslate&&(n.currentTranslate=n.startTranslate),!i.allowSlidePrev&&!i.allowSlideNext&&(n.currentTranslate=n.startTranslate),a.threshold>0)if(Math.abs(h)>a.threshold||n.allowThresholdMove){if(!n.allowThresholdMove){n.allowThresholdMove=!0,s.startX=s.currentX,s.startY=s.currentY,n.currentTranslate=n.startTranslate,s.diff=i.isHorizontal()?s.currentX-s.startX:s.currentY-s.startY;return}}else{n.currentTranslate=n.startTranslate;return}!a.followFinger||a.cssMode||((a.freeMode&&a.freeMode.enabled&&i.freeMode||a.watchSlidesProgress)&&(i.updateActiveIndex(),i.updateSlidesClasses()),a.freeMode&&a.freeMode.enabled&&i.freeMode&&i.freeMode.onTouchMove(),i.updateProgress(n.currentTranslate),i.setTranslate(n.currentTranslate))}function kt(t){const e=this,i=e.touchEventsData,n=i.evCache.findIndex(u=>u.pointerId===t.pointerId);if(n>=0&&i.evCache.splice(n,1),["pointercancel","pointerout","pointerleave","contextmenu"].includes(t.type)&&!(["pointercancel","contextmenu"].includes(t.type)&&(e.browser.isSafari||e.browser.isWebView)))return;const{params:a,touches:s,rtlTranslate:o,slidesGrid:l,enabled:r}=e;if(!r||!a.simulateTouch&&t.pointerType==="mouse")return;let c=t;if(c.originalEvent&&(c=c.originalEvent),i.allowTouchCallbacks&&e.emit("touchEnd",c),i.allowTouchCallbacks=!1,!i.isTouched){i.isMoved&&a.grabCursor&&e.setGrabCursor(!1),i.isMoved=!1,i.startMoving=!1;return}a.grabCursor&&i.isMoved&&i.isTouched&&(e.allowSlideNext===!0||e.allowSlidePrev===!0)&&e.setGrabCursor(!1);const f=Z(),p=f-i.touchStartTime;if(e.allowClick){const u=c.path||c.composedPath&&c.composedPath();e.updateClickedSlide(u&&u[0]||c.target,u),e.emit("tap click",c),p<300&&f-i.lastClickTime<300&&e.emit("doubleTap doubleClick",c)}if(i.lastClickTime=Z(),le(()=>{e.destroyed||(e.allowClick=!0)}),!i.isTouched||!i.isMoved||!e.swipeDirection||s.diff===0||i.currentTranslate===i.startTranslate){i.isTouched=!1,i.isMoved=!1,i.startMoving=!1;return}i.isTouched=!1,i.isMoved=!1,i.startMoving=!1;let S;if(a.followFinger?S=o?e.translate:-e.translate:S=-i.currentTranslate,a.cssMode)return;if(a.freeMode&&a.freeMode.enabled){e.freeMode.onTouchEnd({currentPos:S});return}let m=0,b=e.slidesSizesGrid[0];for(let u=0;u<l.length;u+=u<a.slidesPerGroupSkip?1:a.slidesPerGroup){const v=u<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;typeof l[u+v]<"u"?S>=l[u]&&S<l[u+v]&&(m=u,b=l[u+v]-l[u]):S>=l[u]&&(m=u,b=l[l.length-1]-l[l.length-2])}let h=null,y=null;a.rewind&&(e.isBeginning?y=a.virtual&&a.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(h=0));const w=(S-l[m])/b,d=m<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;if(p>a.longSwipesMs){if(!a.longSwipes){e.slideTo(e.activeIndex);return}e.swipeDirection==="next"&&(w>=a.longSwipesRatio?e.slideTo(a.rewind&&e.isEnd?h:m+d):e.slideTo(m)),e.swipeDirection==="prev"&&(w>1-a.longSwipesRatio?e.slideTo(m+d):y!==null&&w<0&&Math.abs(w)>a.longSwipesRatio?e.slideTo(y):e.slideTo(m))}else{if(!a.shortSwipes){e.slideTo(e.activeIndex);return}e.navigation&&(c.target===e.navigation.nextEl||c.target===e.navigation.prevEl)?c.target===e.navigation.nextEl?e.slideTo(m+d):e.slideTo(m):(e.swipeDirection==="next"&&e.slideTo(h!==null?h:m+d),e.swipeDirection==="prev"&&e.slideTo(y!==null?y:m))}}function me(){const t=this,{params:e,el:i}=t;if(i&&i.offsetWidth===0)return;e.breakpoints&&t.setBreakpoint();const{allowSlideNext:n,allowSlidePrev:a,snapGrid:s}=t,o=t.virtual&&t.params.virtual.enabled;t.allowSlideNext=!0,t.allowSlidePrev=!0,t.updateSize(),t.updateSlides(),t.updateSlidesClasses();const l=o&&e.loop;(e.slidesPerView==="auto"||e.slidesPerView>1)&&t.isEnd&&!t.isBeginning&&!t.params.centeredSlides&&!l?t.slideTo(t.slides.length-1,0,!1,!0):t.params.loop&&!o?t.slideToLoop(t.realIndex,0,!1,!0):t.slideTo(t.activeIndex,0,!1,!0),t.autoplay&&t.autoplay.running&&t.autoplay.paused&&(clearTimeout(t.autoplay.resizeTimeout),t.autoplay.resizeTimeout=setTimeout(()=>{t.autoplay&&t.autoplay.running&&t.autoplay.paused&&t.autoplay.resume()},500)),t.allowSlidePrev=a,t.allowSlideNext=n,t.params.watchOverflow&&s!==t.snapGrid&&t.checkOverflow()}function Rt(t){const e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&t.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(t.stopPropagation(),t.stopImmediatePropagation())))}function Ht(){const t=this,{wrapperEl:e,rtlTranslate:i,enabled:n}=t;if(!n)return;t.previousTranslate=t.translate,t.isHorizontal()?t.translate=-e.scrollLeft:t.translate=-e.scrollTop,t.translate===0&&(t.translate=0),t.updateActiveIndex(),t.updateSlidesClasses();let a;const s=t.maxTranslate()-t.minTranslate();s===0?a=0:a=(t.translate-t.minTranslate())/s,a!==t.progress&&t.updateProgress(i?-t.translate:t.translate),t.emit("setTranslate",t.translate,!1)}function jt(t){const e=this;Q(e,t.target),!(e.params.cssMode||e.params.slidesPerView!=="auto"&&!e.params.autoHeight)&&e.update()}let he=!1;function Wt(){}const Le=(t,e)=>{const i=k(),{params:n,el:a,wrapperEl:s,device:o}=t,l=!!n.nested,r=e==="on"?"addEventListener":"removeEventListener",c=e;a[r]("pointerdown",t.onTouchStart,{passive:!1}),i[r]("pointermove",t.onTouchMove,{passive:!1,capture:l}),i[r]("pointerup",t.onTouchEnd,{passive:!0}),i[r]("pointercancel",t.onTouchEnd,{passive:!0}),i[r]("pointerout",t.onTouchEnd,{passive:!0}),i[r]("pointerleave",t.onTouchEnd,{passive:!0}),i[r]("contextmenu",t.onTouchEnd,{passive:!0}),(n.preventClicks||n.preventClicksPropagation)&&a[r]("click",t.onClick,!0),n.cssMode&&s[r]("scroll",t.onScroll),n.updateOnWindowResize?t[c](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",me,!0):t[c]("observerUpdate",me,!0),a[r]("load",t.onLoad,{capture:!0})};function qt(){const t=this,e=k(),{params:i}=t;t.onTouchStart=Vt.bind(t),t.onTouchMove=Ft.bind(t),t.onTouchEnd=kt.bind(t),i.cssMode&&(t.onScroll=Ht.bind(t)),t.onClick=Rt.bind(t),t.onLoad=jt.bind(t),he||(e.addEventListener("touchstart",Wt),he=!0),Le(t,"on")}function Xt(){Le(this,"off")}var Yt={attachEvents:qt,detachEvents:Xt};const ge=(t,e)=>t.grid&&e.grid&&e.grid.rows>1;function Ut(){const t=this,{realIndex:e,initialized:i,params:n,el:a}=t,s=n.breakpoints;if(!s||s&&Object.keys(s).length===0)return;const o=t.getBreakpoint(s,t.params.breakpointsBase,t.el);if(!o||t.currentBreakpoint===o)return;const r=(o in s?s[o]:void 0)||t.originalParams,c=ge(t,n),f=ge(t,r),p=n.enabled;c&&!f?(a.classList.remove(`${n.containerModifierClass}grid`,`${n.containerModifierClass}grid-column`),t.emitContainerClasses()):!c&&f&&(a.classList.add(`${n.containerModifierClass}grid`),(r.grid.fill&&r.grid.fill==="column"||!r.grid.fill&&n.grid.fill==="column")&&a.classList.add(`${n.containerModifierClass}grid-column`),t.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach(w=>{if(typeof r[w]>"u")return;const d=n[w]&&n[w].enabled,u=r[w]&&r[w].enabled;d&&!u&&t[w].disable(),!d&&u&&t[w].enable()});const S=r.direction&&r.direction!==n.direction,m=n.loop&&(r.slidesPerView!==n.slidesPerView||S),b=n.loop;S&&i&&t.changeDirection(),_(t.params,r);const h=t.params.enabled,y=t.params.loop;Object.assign(t,{allowTouchMove:t.params.allowTouchMove,allowSlideNext:t.params.allowSlideNext,allowSlidePrev:t.params.allowSlidePrev}),p&&!h?t.disable():!p&&h&&t.enable(),t.currentBreakpoint=o,t.emit("_beforeBreakpoint",r),i&&(m?(t.loopDestroy(),t.loopCreate(e),t.updateSlides()):!b&&y?(t.loopCreate(e),t.updateSlides()):b&&!y&&t.loopDestroy()),t.emit("breakpoint",r)}function Kt(t,e,i){if(e===void 0&&(e="window"),!t||e==="container"&&!i)return;let n=!1;const a=G(),s=e==="window"?a.innerHeight:i.clientHeight,o=Object.keys(t).map(l=>{if(typeof l=="string"&&l.indexOf("@")===0){const r=parseFloat(l.substr(1));return{value:s*r,point:l}}return{value:l,point:l}});o.sort((l,r)=>parseInt(l.value,10)-parseInt(r.value,10));for(let l=0;l<o.length;l+=1){const{point:r,value:c}=o[l];e==="window"?a.matchMedia(`(min-width: ${c}px)`).matches&&(n=r):c<=i.clientWidth&&(n=r)}return n||"max"}var Qt={setBreakpoint:Ut,getBreakpoint:Kt};function Zt(t,e){const i=[];return t.forEach(n=>{typeof n=="object"?Object.keys(n).forEach(a=>{n[a]&&i.push(e+a)}):typeof n=="string"&&i.push(e+n)}),i}function Jt(){const t=this,{classNames:e,params:i,rtl:n,el:a,device:s}=t,o=Zt(["initialized",i.direction,{"free-mode":t.params.freeMode&&i.freeMode.enabled},{autoheight:i.autoHeight},{rtl:n},{grid:i.grid&&i.grid.rows>1},{"grid-column":i.grid&&i.grid.rows>1&&i.grid.fill==="column"},{android:s.android},{ios:s.ios},{"css-mode":i.cssMode},{centered:i.cssMode&&i.centeredSlides},{"watch-progress":i.watchSlidesProgress}],i.containerModifierClass);e.push(...o),a.classList.add(...e),t.emitContainerClasses()}function ei(){const t=this,{el:e,classNames:i}=t;e.classList.remove(...i),t.emitContainerClasses()}var ti={addClasses:Jt,removeClasses:ei};function ii(){const t=this,{isLocked:e,params:i}=t,{slidesOffsetBefore:n}=i;if(n){const a=t.slides.length-1,s=t.slidesGrid[a]+t.slidesSizesGrid[a]+n*2;t.isLocked=t.size>s}else t.isLocked=t.snapGrid.length===1;i.allowSlideNext===!0&&(t.allowSlideNext=!t.isLocked),i.allowSlidePrev===!0&&(t.allowSlidePrev=!t.isLocked),e&&e!==t.isLocked&&(t.isEnd=!1),e!==t.isLocked&&t.emit(t.isLocked?"lock":"unlock")}var ni={checkOverflow:ii},ue={init:!0,direction:"horizontal",oneWayMovement:!1,touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopedSlides:null,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function si(t,e){return function(n){n===void 0&&(n={});const a=Object.keys(n)[0],s=n[a];if(typeof s!="object"||s===null){_(e,n);return}if(t[a]===!0&&(t[a]={enabled:!0}),a==="navigation"&&t[a]&&t[a].enabled&&!t[a].prevEl&&!t[a].nextEl&&(t[a].auto=!0),["pagination","scrollbar"].indexOf(a)>=0&&t[a]&&t[a].enabled&&!t[a].el&&(t[a].auto=!0),!(a in t&&"enabled"in s)){_(e,n);return}typeof t[a]=="object"&&!("enabled"in t[a])&&(t[a].enabled=!0),t[a]||(t[a]={enabled:!1}),_(e,n)}}const se={eventsEmitter:tt,update:ct,translate:wt,transition:xt,slide:It,loop:Dt,grabCursor:Nt,events:Yt,breakpoints:Qt,checkOverflow:ni,classes:ti},ae={};let ce=class R{constructor(){let e,i;for(var n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];a.length===1&&a[0].constructor&&Object.prototype.toString.call(a[0]).slice(8,-1)==="Object"?i=a[0]:[e,i]=a,i||(i={}),i=_({},i),e&&!i.el&&(i.el=e);const o=k();if(i.el&&typeof i.el=="string"&&o.querySelectorAll(i.el).length>1){const f=[];return o.querySelectorAll(i.el).forEach(p=>{const S=_({},i,{el:p});f.push(new R(S))}),f}const l=this;l.__swiper__=!0,l.support=Pe(),l.device=Ke({userAgent:i.userAgent}),l.browser=Ze(),l.eventsListeners={},l.eventsAnyListeners=[],l.modules=[...l.__modules__],i.modules&&Array.isArray(i.modules)&&l.modules.push(...i.modules);const r={};l.modules.forEach(f=>{f({params:i,swiper:l,extendParams:si(i,r),on:l.on.bind(l),once:l.once.bind(l),off:l.off.bind(l),emit:l.emit.bind(l)})});const c=_({},ue,r);return l.params=_({},c,ae,i),l.originalParams=_({},l.params),l.passedParams=_({},i),l.params&&l.params.on&&Object.keys(l.params.on).forEach(f=>{l.on(f,l.params.on[f])}),l.params&&l.params.onAny&&l.onAny(l.params.onAny),Object.assign(l,{enabled:l.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return l.params.direction==="horizontal"},isVertical(){return l.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:l.params.allowSlideNext,allowSlidePrev:l.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:l.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,evCache:[]},allowClick:!0,allowTouchMove:l.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),l.emit("_swiper"),l.params.init&&l.init(),l}getSlideIndex(e){const{slidesEl:i,params:n}=this,a=F(i,`.${n.slideClass}, swiper-slide`),s=J(a[0]);return J(e)-s}getSlideIndexByData(e){return this.getSlideIndex(this.slides.filter(i=>i.getAttribute("data-swiper-slide-index")*1===e)[0])}recalcSlides(){const e=this,{slidesEl:i,params:n}=e;e.slides=F(i,`.${n.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,i){const n=this;e=Math.min(Math.max(e,0),1);const a=n.minTranslate(),o=(n.maxTranslate()-a)*e+a;n.translateTo(o,typeof i>"u"?0:i),n.updateActiveIndex(),n.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const i=e.el.className.split(" ").filter(n=>n.indexOf("swiper")===0||n.indexOf(e.params.containerModifierClass)===0);e.emit("_containerClasses",i.join(" "))}getSlideClasses(e){const i=this;return i.destroyed?"":e.className.split(" ").filter(n=>n.indexOf("swiper-slide")===0||n.indexOf(i.params.slideClass)===0).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const i=[];e.slides.forEach(n=>{const a=e.getSlideClasses(n);i.push({slideEl:n,classNames:a}),e.emit("_slideClass",n,a)}),e.emit("_slideClasses",i)}slidesPerViewDynamic(e,i){e===void 0&&(e="current"),i===void 0&&(i=!1);const n=this,{params:a,slides:s,slidesGrid:o,slidesSizesGrid:l,size:r,activeIndex:c}=n;let f=1;if(typeof a.slidesPerView=="number")return a.slidesPerView;if(a.centeredSlides){let p=s[c]?s[c].swiperSlideSize:0,S;for(let m=c+1;m<s.length;m+=1)s[m]&&!S&&(p+=s[m].swiperSlideSize,f+=1,p>r&&(S=!0));for(let m=c-1;m>=0;m-=1)s[m]&&!S&&(p+=s[m].swiperSlideSize,f+=1,p>r&&(S=!0))}else if(e==="current")for(let p=c+1;p<s.length;p+=1)(i?o[p]+l[p]-o[c]<r:o[p]-o[c]<r)&&(f+=1);else for(let p=c-1;p>=0;p-=1)o[c]-o[p]<r&&(f+=1);return f}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:i,params:n}=e;n.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(o=>{o.complete&&Q(e,o)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses();function a(){const o=e.rtlTranslate?e.translate*-1:e.translate,l=Math.min(Math.max(o,e.maxTranslate()),e.minTranslate());e.setTranslate(l),e.updateActiveIndex(),e.updateSlidesClasses()}let s;if(n.freeMode&&n.freeMode.enabled&&!n.cssMode)a(),n.autoHeight&&e.updateAutoHeight();else{if((n.slidesPerView==="auto"||n.slidesPerView>1)&&e.isEnd&&!n.centeredSlides){const o=e.virtual&&n.virtual.enabled?e.virtual.slides:e.slides;s=e.slideTo(o.length-1,0,!1,!0)}else s=e.slideTo(e.activeIndex,0,!1,!0);s||a()}n.watchOverflow&&i!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,i){i===void 0&&(i=!0);const n=this,a=n.params.direction;return e||(e=a==="horizontal"?"vertical":"horizontal"),e===a||e!=="horizontal"&&e!=="vertical"||(n.el.classList.remove(`${n.params.containerModifierClass}${a}`),n.el.classList.add(`${n.params.containerModifierClass}${e}`),n.emitContainerClasses(),n.params.direction=e,n.slides.forEach(s=>{e==="vertical"?s.style.width="":s.style.height=""}),n.emit("changeDirection"),i&&n.update()),n}changeLanguageDirection(e){const i=this;i.rtl&&e==="rtl"||!i.rtl&&e==="ltr"||(i.rtl=e==="rtl",i.rtlTranslate=i.params.direction==="horizontal"&&i.rtl,i.rtl?(i.el.classList.add(`${i.params.containerModifierClass}rtl`),i.el.dir="rtl"):(i.el.classList.remove(`${i.params.containerModifierClass}rtl`),i.el.dir="ltr"),i.update())}mount(e){const i=this;if(i.mounted)return!0;let n=e||i.params.el;if(typeof n=="string"&&(n=document.querySelector(n)),!n)return!1;n.swiper=i,n.parentNode&&n.parentNode.host&&n.parentNode.host.nodeName==="SWIPER-CONTAINER"&&(i.isElement=!0);const a=()=>`.${(i.params.wrapperClass||"").trim().split(" ").join(".")}`;let o=(()=>n&&n.shadowRoot&&n.shadowRoot.querySelector?n.shadowRoot.querySelector(a()):F(n,a())[0])();return!o&&i.params.createElements&&(o=Ee("div",i.params.wrapperClass),n.append(o),F(n,`.${i.params.slideClass}`).forEach(l=>{o.append(l)})),Object.assign(i,{el:n,wrapperEl:o,slidesEl:i.isElement&&!n.parentNode.host.slideSlots?n.parentNode.host:o,hostEl:i.isElement?n.parentNode.host:n,mounted:!0,rtl:n.dir.toLowerCase()==="rtl"||j(n,"direction")==="rtl",rtlTranslate:i.params.direction==="horizontal"&&(n.dir.toLowerCase()==="rtl"||j(n,"direction")==="rtl"),wrongRTL:j(o,"display")==="-webkit-box"}),!0}init(e){const i=this;if(i.initialized||i.mount(e)===!1)return i;i.emit("beforeInit"),i.params.breakpoints&&i.setBreakpoint(),i.addClasses(),i.updateSize(),i.updateSlides(),i.params.watchOverflow&&i.checkOverflow(),i.params.grabCursor&&i.enabled&&i.setGrabCursor(),i.params.loop&&i.virtual&&i.params.virtual.enabled?i.slideTo(i.params.initialSlide+i.virtual.slidesBefore,0,i.params.runCallbacksOnInit,!1,!0):i.slideTo(i.params.initialSlide,0,i.params.runCallbacksOnInit,!1,!0),i.params.loop&&i.loopCreate(),i.attachEvents();const a=[...i.el.querySelectorAll('[loading="lazy"]')];return i.isElement&&a.push(...i.hostEl.querySelectorAll('[loading="lazy"]')),a.forEach(s=>{s.complete?Q(i,s):s.addEventListener("load",o=>{Q(i,o.target)})}),de(i),i.initialized=!0,de(i),i.emit("init"),i.emit("afterInit"),i}destroy(e,i){e===void 0&&(e=!0),i===void 0&&(i=!0);const n=this,{params:a,el:s,wrapperEl:o,slides:l}=n;return typeof n.params>"u"||n.destroyed||(n.emit("beforeDestroy"),n.initialized=!1,n.detachEvents(),a.loop&&n.loopDestroy(),i&&(n.removeClasses(),s.removeAttribute("style"),o.removeAttribute("style"),l&&l.length&&l.forEach(r=>{r.classList.remove(a.slideVisibleClass,a.slideActiveClass,a.slideNextClass,a.slidePrevClass),r.removeAttribute("style"),r.removeAttribute("data-swiper-slide-index")})),n.emit("destroy"),Object.keys(n.eventsListeners).forEach(r=>{n.off(r)}),e!==!1&&(n.el.swiper=null,Re(n)),n.destroyed=!0),null}static extendDefaults(e){_(ae,e)}static get extendedDefaults(){return ae}static get defaults(){return ue}static installModule(e){R.prototype.__modules__||(R.prototype.__modules__=[]);const i=R.prototype.__modules__;typeof e=="function"&&i.indexOf(e)<0&&i.push(e)}static use(e){return Array.isArray(e)?(e.forEach(i=>R.installModule(i)),R):(R.installModule(e),R)}};Object.keys(se).forEach(t=>{Object.keys(se[t]).forEach(e=>{ce.prototype[e]=se[t][e]})});ce.use([Je,et]);const Oe=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopedSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideNextClass","slidePrevClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function q(t){return typeof t=="object"&&t!==null&&t.constructor&&Object.prototype.toString.call(t).slice(8,-1)==="Object"&&!t.__swiper__}function W(t,e){const i=["__proto__","constructor","prototype"];Object.keys(e).filter(n=>i.indexOf(n)<0).forEach(n=>{typeof t[n]>"u"?t[n]=e[n]:q(e[n])&&q(t[n])&&Object.keys(e[n]).length>0?e[n].__swiper__?t[n]=e[n]:W(t[n],e[n]):t[n]=e[n]})}function Ie(t){return t===void 0&&(t={}),t.navigation&&typeof t.navigation.nextEl>"u"&&typeof t.navigation.prevEl>"u"}function ze(t){return t===void 0&&(t={}),t.pagination&&typeof t.pagination.el>"u"}function Ae(t){return t===void 0&&(t={}),t.scrollbar&&typeof t.scrollbar.el>"u"}function Be(t){t===void 0&&(t="");const e=t.split(" ").map(n=>n.trim()).filter(n=>!!n),i=[];return e.forEach(n=>{i.indexOf(n)<0&&i.push(n)}),i.join(" ")}function ai(t){return t===void 0&&(t=""),t?t.includes("swiper-wrapper")?t:`swiper-wrapper ${t}`:"swiper-wrapper"}function ri(t){let{swiper:e,slides:i,passedParams:n,changedParams:a,nextEl:s,prevEl:o,scrollbarEl:l,paginationEl:r}=t;const c=a.filter(g=>g!=="children"&&g!=="direction"&&g!=="wrapperClass"),{params:f,pagination:p,navigation:S,scrollbar:m,virtual:b,thumbs:h}=e;let y,w,d,u,v,x,L,E;a.includes("thumbs")&&n.thumbs&&n.thumbs.swiper&&f.thumbs&&!f.thumbs.swiper&&(y=!0),a.includes("controller")&&n.controller&&n.controller.control&&f.controller&&!f.controller.control&&(w=!0),a.includes("pagination")&&n.pagination&&(n.pagination.el||r)&&(f.pagination||f.pagination===!1)&&p&&!p.el&&(d=!0),a.includes("scrollbar")&&n.scrollbar&&(n.scrollbar.el||l)&&(f.scrollbar||f.scrollbar===!1)&&m&&!m.el&&(u=!0),a.includes("navigation")&&n.navigation&&(n.navigation.prevEl||o)&&(n.navigation.nextEl||s)&&(f.navigation||f.navigation===!1)&&S&&!S.prevEl&&!S.nextEl&&(v=!0);const M=g=>{e[g]&&(e[g].destroy(),g==="navigation"?(e.isElement&&(e[g].prevEl.remove(),e[g].nextEl.remove()),f[g].prevEl=void 0,f[g].nextEl=void 0,e[g].prevEl=void 0,e[g].nextEl=void 0):(e.isElement&&e[g].el.remove(),f[g].el=void 0,e[g].el=void 0))};a.includes("loop")&&e.isElement&&(f.loop&&!n.loop?x=!0:!f.loop&&n.loop?L=!0:E=!0),c.forEach(g=>{if(q(f[g])&&q(n[g]))W(f[g],n[g]),(g==="navigation"||g==="pagination"||g==="scrollbar")&&"enabled"in n[g]&&!n[g].enabled&&M(g);else{const O=n[g];(O===!0||O===!1)&&(g==="navigation"||g==="pagination"||g==="scrollbar")?O===!1&&M(g):f[g]=n[g]}}),c.includes("controller")&&!w&&e.controller&&e.controller.control&&f.controller&&f.controller.control&&(e.controller.control=f.controller.control),a.includes("children")&&i&&b&&f.virtual.enabled&&(b.slides=i,b.update(!0)),a.includes("children")&&i&&f.loop&&(E=!0),y&&h.init()&&h.update(!0),w&&(e.controller.control=f.controller.control),d&&(e.isElement&&(!r||typeof r=="string")&&(r=document.createElement("div"),r.classList.add("swiper-pagination"),r.part.add("pagination"),e.el.appendChild(r)),r&&(f.pagination.el=r),p.init(),p.render(),p.update()),u&&(e.isElement&&(!l||typeof l=="string")&&(l=document.createElement("div"),l.classList.add("swiper-scrollbar"),l.part.add("scrollbar"),e.el.appendChild(l)),l&&(f.scrollbar.el=l),m.init(),m.updateSize(),m.setTranslate()),v&&(e.isElement&&((!s||typeof s=="string")&&(s=document.createElement("div"),s.classList.add("swiper-button-next"),s.innerHTML=e.hostEl.constructor.nextButtonSvg,s.part.add("button-next"),e.el.appendChild(s)),(!o||typeof o=="string")&&(o=document.createElement("div"),o.classList.add("swiper-button-prev"),o.innerHTML=e.hostEl.constructor.prevButtonSvg,o.part.add("button-prev"),e.el.appendChild(o))),s&&(f.navigation.nextEl=s),o&&(f.navigation.prevEl=o),S.init(),S.update()),a.includes("allowSlideNext")&&(e.allowSlideNext=n.allowSlideNext),a.includes("allowSlidePrev")&&(e.allowSlidePrev=n.allowSlidePrev),a.includes("direction")&&e.changeDirection(n.direction,!1),(x||E)&&e.loopDestroy(),(L||E)&&e.loopCreate(),e.update()}function ve(t,e){t===void 0&&(t={}),e===void 0&&(e=!0);const i={on:{}},n={},a={};W(i,ue),i._emitClasses=!0,i.init=!1;const s={},o=Oe.map(r=>r.replace(/_/,"")),l=Object.assign({},t);return Object.keys(l).forEach(r=>{typeof t[r]>"u"||(o.indexOf(r)>=0?q(t[r])?(i[r]={},a[r]={},W(i[r],t[r]),W(a[r],t[r])):(i[r]=t[r],a[r]=t[r]):r.search(/on[A-Z]/)===0&&typeof t[r]=="function"?e?n[`${r[2].toLowerCase()}${r.substr(3)}`]=t[r]:i.on[`${r[2].toLowerCase()}${r.substr(3)}`]=t[r]:s[r]=t[r])}),["navigation","pagination","scrollbar"].forEach(r=>{i[r]===!0&&(i[r]={}),i[r]===!1&&delete i[r]}),{params:i,passedParams:a,rest:s,events:n}}function li(t,e){let{el:i,nextEl:n,prevEl:a,paginationEl:s,scrollbarEl:o,swiper:l}=t;Ie(e)&&n&&a&&(l.params.navigation.nextEl=n,l.originalParams.navigation.nextEl=n,l.params.navigation.prevEl=a,l.originalParams.navigation.prevEl=a),ze(e)&&s&&(l.params.pagination.el=s,l.originalParams.pagination.el=s),Ae(e)&&o&&(l.params.scrollbar.el=o,l.originalParams.scrollbar.el=o),l.init(i)}function oi(t,e,i,n,a){const s=[];if(!e)return s;const o=r=>{s.indexOf(r)<0&&s.push(r)};if(i&&n){const r=n.map(a),c=i.map(a);r.join("")!==c.join("")&&o("children"),n.length!==i.length&&o("children")}return Oe.filter(r=>r[0]==="_").map(r=>r.replace(/_/,"")).forEach(r=>{if(r in t&&r in e)if(q(t[r])&&q(e[r])){const c=Object.keys(t[r]),f=Object.keys(e[r]);c.length!==f.length?o(r):(c.forEach(p=>{t[r][p]!==e[r][p]&&o(r)}),f.forEach(p=>{t[r][p]!==e[r][p]&&o(r)}))}else t[r]!==e[r]&&o(r)}),s}const di=t=>{!t||t.destroyed||!t.params.virtual||t.params.virtual&&!t.params.virtual.enabled||(t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),t.parallax&&t.params.parallax&&t.params.parallax.enabled&&t.parallax.setTranslate())};function re(t,e,i){t===void 0&&(t={});const n=[],a={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]},s=(o,l)=>{Array.isArray(o)&&o.forEach(r=>{const c=typeof r.type=="symbol";l==="default"&&(l="container-end"),c&&r.children?s(r.children,l):r.type&&(r.type.name==="SwiperSlide"||r.type.name==="AsyncComponentWrapper")?n.push(r):a[l]&&a[l].push(r)})};return Object.keys(t).forEach(o=>{if(typeof t[o]!="function")return;const l=t[o]();s(l,o)}),i.value=e.value,e.value=n,{slides:n,slots:a}}function ui(t,e,i){if(!i)return null;const n=f=>{let p=f;return f<0?p=e.length+f:p>=e.length&&(p=p-e.length),p},a=t.value.isHorizontal()?{[t.value.rtlTranslate?"right":"left"]:`${i.offset}px`}:{top:`${i.offset}px`},{from:s,to:o}=i,l=t.value.params.loop?-e.length:0,r=t.value.params.loop?e.length*2:e.length,c=[];for(let f=l;f<r;f+=1)f>=s&&f<=o&&c.push(e[n(f)]);return c.map(f=>(f.props||(f.props={}),f.props.style||(f.props.style={}),f.props.swiperRef=t,f.props.style=a,V(f.type,{...f.props},f.children)))}const ci={name:"Swiper",props:{tag:{type:String,default:"div"},wrapperTag:{type:String,default:"div"},modules:{type:Array,default:void 0},init:{type:Boolean,default:void 0},direction:{type:String,default:void 0},oneWayMovement:{type:Boolean,default:void 0},touchEventsTarget:{type:String,default:void 0},initialSlide:{type:Number,default:void 0},speed:{type:Number,default:void 0},cssMode:{type:Boolean,default:void 0},updateOnWindowResize:{type:Boolean,default:void 0},resizeObserver:{type:Boolean,default:void 0},nested:{type:Boolean,default:void 0},focusableElements:{type:String,default:void 0},width:{type:Number,default:void 0},height:{type:Number,default:void 0},preventInteractionOnTransition:{type:Boolean,default:void 0},userAgent:{type:String,default:void 0},url:{type:String,default:void 0},edgeSwipeDetection:{type:[Boolean,String],default:void 0},edgeSwipeThreshold:{type:Number,default:void 0},autoHeight:{type:Boolean,default:void 0},setWrapperSize:{type:Boolean,default:void 0},virtualTranslate:{type:Boolean,default:void 0},effect:{type:String,default:void 0},breakpoints:{type:Object,default:void 0},spaceBetween:{type:[Number,String],default:void 0},slidesPerView:{type:[Number,String],default:void 0},maxBackfaceHiddenSlides:{type:Number,default:void 0},slidesPerGroup:{type:Number,default:void 0},slidesPerGroupSkip:{type:Number,default:void 0},slidesPerGroupAuto:{type:Boolean,default:void 0},centeredSlides:{type:Boolean,default:void 0},centeredSlidesBounds:{type:Boolean,default:void 0},slidesOffsetBefore:{type:Number,default:void 0},slidesOffsetAfter:{type:Number,default:void 0},normalizeSlideIndex:{type:Boolean,default:void 0},centerInsufficientSlides:{type:Boolean,default:void 0},watchOverflow:{type:Boolean,default:void 0},roundLengths:{type:Boolean,default:void 0},touchRatio:{type:Number,default:void 0},touchAngle:{type:Number,default:void 0},simulateTouch:{type:Boolean,default:void 0},shortSwipes:{type:Boolean,default:void 0},longSwipes:{type:Boolean,default:void 0},longSwipesRatio:{type:Number,default:void 0},longSwipesMs:{type:Number,default:void 0},followFinger:{type:Boolean,default:void 0},allowTouchMove:{type:Boolean,default:void 0},threshold:{type:Number,default:void 0},touchMoveStopPropagation:{type:Boolean,default:void 0},touchStartPreventDefault:{type:Boolean,default:void 0},touchStartForcePreventDefault:{type:Boolean,default:void 0},touchReleaseOnEdges:{type:Boolean,default:void 0},uniqueNavElements:{type:Boolean,default:void 0},resistance:{type:Boolean,default:void 0},resistanceRatio:{type:Number,default:void 0},watchSlidesProgress:{type:Boolean,default:void 0},grabCursor:{type:Boolean,default:void 0},preventClicks:{type:Boolean,default:void 0},preventClicksPropagation:{type:Boolean,default:void 0},slideToClickedSlide:{type:Boolean,default:void 0},loop:{type:Boolean,default:void 0},loopedSlides:{type:Number,default:void 0},loopPreventsSliding:{type:Boolean,default:void 0},rewind:{type:Boolean,default:void 0},allowSlidePrev:{type:Boolean,default:void 0},allowSlideNext:{type:Boolean,default:void 0},swipeHandler:{type:Boolean,default:void 0},noSwiping:{type:Boolean,default:void 0},noSwipingClass:{type:String,default:void 0},noSwipingSelector:{type:String,default:void 0},passiveListeners:{type:Boolean,default:void 0},containerModifierClass:{type:String,default:void 0},slideClass:{type:String,default:void 0},slideActiveClass:{type:String,default:void 0},slideVisibleClass:{type:String,default:void 0},slideNextClass:{type:String,default:void 0},slidePrevClass:{type:String,default:void 0},wrapperClass:{type:String,default:void 0},lazyPreloaderClass:{type:String,default:void 0},lazyPreloadPrevNext:{type:Number,default:void 0},runCallbacksOnInit:{type:Boolean,default:void 0},observer:{type:Boolean,default:void 0},observeParents:{type:Boolean,default:void 0},observeSlideChildren:{type:Boolean,default:void 0},a11y:{type:[Boolean,Object],default:void 0},autoplay:{type:[Boolean,Object],default:void 0},controller:{type:Object,default:void 0},coverflowEffect:{type:Object,default:void 0},cubeEffect:{type:Object,default:void 0},fadeEffect:{type:Object,default:void 0},flipEffect:{type:Object,default:void 0},creativeEffect:{type:Object,default:void 0},cardsEffect:{type:Object,default:void 0},hashNavigation:{type:[Boolean,Object],default:void 0},history:{type:[Boolean,Object],default:void 0},keyboard:{type:[Boolean,Object],default:void 0},mousewheel:{type:[Boolean,Object],default:void 0},navigation:{type:[Boolean,Object],default:void 0},pagination:{type:[Boolean,Object],default:void 0},parallax:{type:[Boolean,Object],default:void 0},scrollbar:{type:[Boolean,Object],default:void 0},thumbs:{type:Object,default:void 0},virtual:{type:[Boolean,Object],default:void 0},zoom:{type:[Boolean,Object],default:void 0},grid:{type:[Object],default:void 0},freeMode:{type:[Boolean,Object],default:void 0},enabled:{type:Boolean,default:void 0}},emits:["_beforeBreakpoint","_containerClasses","_slideClass","_slideClasses","_swiper","_freeModeNoMomentumRelease","activeIndexChange","afterInit","autoplay","autoplayStart","autoplayStop","autoplayPause","autoplayResume","autoplayTimeLeft","beforeDestroy","beforeInit","beforeLoopFix","beforeResize","beforeSlideChangeStart","beforeTransitionStart","breakpoint","breakpointsBase","changeDirection","click","disable","doubleTap","doubleClick","destroy","enable","fromEdge","hashChange","hashSet","init","keyPress","lock","loopFix","momentumBounce","navigationHide","navigationShow","navigationPrev","navigationNext","observerUpdate","orientationchange","paginationHide","paginationRender","paginationShow","paginationUpdate","progress","reachBeginning","reachEnd","realIndexChange","resize","scroll","scrollbarDragEnd","scrollbarDragMove","scrollbarDragStart","setTransition","setTranslate","slideChange","slideChangeTransitionEnd","slideChangeTransitionStart","slideNextTransitionEnd","slideNextTransitionStart","slidePrevTransitionEnd","slidePrevTransitionStart","slideResetTransitionStart","slideResetTransitionEnd","sliderMove","sliderFirstMove","slidesLengthChange","slidesGridLengthChange","snapGridLengthChange","snapIndexChange","swiper","tap","toEdge","touchEnd","touchMove","touchMoveOpposite","touchStart","transitionEnd","transitionStart","unlock","update","virtualUpdate","zoomChange"],setup(t,e){let{slots:i,emit:n}=e;const{tag:a,wrapperTag:s}=t,o=D("swiper"),l=D(null),r=D(!1),c=D(!1),f=D(null),p=D(null),S=D(null),m={value:[]},b={value:[]},h=D(null),y=D(null),w=D(null),d=D(null),{params:u,passedParams:v}=ve(t,!1);re(i,m,b),S.value=v,b.value=m.value;const x=()=>{re(i,m,b),r.value=!0};u.onAny=function(M){for(var g=arguments.length,O=new Array(g>1?g-1:0),T=1;T<g;T++)O[T-1]=arguments[T];n(M,...O)},Object.assign(u.on,{_beforeBreakpoint:x,_containerClasses(M,g){o.value=g}});const L={...u};if(delete L.wrapperClass,p.value=new ce(L),p.value.virtual&&p.value.params.virtual.enabled){p.value.virtual.slides=m.value;const M={cache:!1,slides:m.value,renderExternal:g=>{l.value=g},renderExternalUpdate:!1};W(p.value.params.virtual,M),W(p.value.originalParams.virtual,M)}we(()=>{!c.value&&p.value&&(p.value.emitSlidesClasses(),c.value=!0);const{passedParams:M}=ve(t,!1),g=oi(M,S.value,m.value,b.value,O=>O.props&&O.props.key);S.value=M,(g.length||r.value)&&p.value&&!p.value.destroyed&&ri({swiper:p.value,slides:m.value,passedParams:M,changedParams:g,nextEl:h.value,prevEl:y.value,scrollbarEl:d.value,paginationEl:w.value}),r.value=!1}),Se("swiper",p),Ne(l,()=>{$e(()=>{di(p.value)})}),ye(()=>{f.value&&(li({el:f.value,nextEl:h.value,prevEl:y.value,paginationEl:w.value,scrollbarEl:d.value,swiper:p.value},u),n("swiper",p.value))}),be(()=>{p.value&&!p.value.destroyed&&p.value.destroy(!0,!1)});function E(M){return u.virtual?ui(p,M,l.value):(M.forEach((g,O)=>{g.props||(g.props={}),g.props.swiperRef=p,g.props.swiperSlideIndex=O}),M)}return()=>{const{slides:M,slots:g}=re(i,m,b);return V(a,{ref:f,class:Be(o.value)},[g["container-start"],V(s,{class:ai(u.wrapperClass)},[g["wrapper-start"],E(M),g["wrapper-end"]]),Ie(t)&&[V("div",{ref:y,class:"swiper-button-prev"}),V("div",{ref:h,class:"swiper-button-next"})],Ae(t)&&V("div",{ref:d,class:"swiper-scrollbar"}),ze(t)&&V("div",{ref:w,class:"swiper-pagination"}),g["container-end"]])}}},pi={name:"SwiperSlide",props:{tag:{type:String,default:"div"},swiperRef:{type:Object,required:!1},swiperSlideIndex:{type:Number,default:void 0,required:!1},zoom:{type:Boolean,default:void 0,required:!1},lazy:{type:Boolean,default:!1,required:!1},virtualIndex:{type:[String,Number],default:void 0}},setup(t,e){let{slots:i}=e,n=!1;const{swiperRef:a}=t,s=D(null),o=D("swiper-slide"),l=D(!1);function r(p,S,m){S===s.value&&(o.value=m)}ye(()=>{!a||!a.value||(a.value.on("_slideClass",r),n=!0)}),Ve(()=>{n||!a||!a.value||(a.value.on("_slideClass",r),n=!0)}),we(()=>{!s.value||!a||!a.value||(typeof t.swiperSlideIndex<"u"&&(s.value.swiperSlideIndex=t.swiperSlideIndex),a.value.destroyed&&o.value!=="swiper-slide"&&(o.value="swiper-slide"))}),be(()=>{!a||!a.value||a.value.off("_slideClass",r)});const c=Fe(()=>({isActive:o.value.indexOf("swiper-slide-active")>=0,isVisible:o.value.indexOf("swiper-slide-visible")>=0,isPrev:o.value.indexOf("swiper-slide-prev")>=0,isNext:o.value.indexOf("swiper-slide-next")>=0}));Se("swiperSlide",c);const f=()=>{l.value=!0};return()=>V(t.tag,{class:Be(`${o.value}`),ref:s,"data-swiper-slide-index":typeof t.virtualIndex>"u"&&a&&a.value&&a.value.params.loop?t.swiperSlideIndex:t.virtualIndex,onLoadCapture:f},t.zoom?V("div",{class:"swiper-zoom-container","data-swiper-zoom":typeof t.zoom=="number"?t.zoom:void 0},[i.default&&i.default(c.value),t.lazy&&!l.value&&V("div",{class:"swiper-lazy-preloader"})]):[i.default&&i.default(c.value),t.lazy&&!l.value&&V("div",{class:"swiper-lazy-preloader"})])}};function De(t,e,i,n){return t.params.createElements&&Object.keys(n).forEach(a=>{if(!i[a]&&i.auto===!0){let s=F(t.el,`.${n[a]}`)[0];s||(s=Ee("div",n[a]),s.className=n[a],t.el.append(s)),i[a]=s,e[a]=s}}),i}function mi(t){let{swiper:e,extendParams:i,on:n,emit:a}=t;i({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),e.navigation={nextEl:null,prevEl:null};const s=h=>(Array.isArray(h)?h:[h]).filter(y=>!!y);function o(h){let y;return h&&typeof h=="string"&&e.isElement&&(y=e.el.querySelector(h),y)?y:(h&&(typeof h=="string"&&(y=[...document.querySelectorAll(h)]),e.params.uniqueNavElements&&typeof h=="string"&&y.length>1&&e.el.querySelectorAll(h).length===1&&(y=e.el.querySelector(h))),h&&!y?h:y)}function l(h,y){const w=e.params.navigation;h=s(h),h.forEach(d=>{d&&(d.classList[y?"add":"remove"](...w.disabledClass.split(" ")),d.tagName==="BUTTON"&&(d.disabled=y),e.params.watchOverflow&&e.enabled&&d.classList[e.isLocked?"add":"remove"](w.lockClass))})}function r(){const{nextEl:h,prevEl:y}=e.navigation;if(e.params.loop){l(y,!1),l(h,!1);return}l(y,e.isBeginning&&!e.params.rewind),l(h,e.isEnd&&!e.params.rewind)}function c(h){h.preventDefault(),!(e.isBeginning&&!e.params.loop&&!e.params.rewind)&&(e.slidePrev(),a("navigationPrev"))}function f(h){h.preventDefault(),!(e.isEnd&&!e.params.loop&&!e.params.rewind)&&(e.slideNext(),a("navigationNext"))}function p(){const h=e.params.navigation;if(e.params.navigation=De(e,e.originalParams.navigation,e.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(h.nextEl||h.prevEl))return;let y=o(h.nextEl),w=o(h.prevEl);Object.assign(e.navigation,{nextEl:y,prevEl:w}),y=s(y),w=s(w);const d=(u,v)=>{u&&u.addEventListener("click",v==="next"?f:c),!e.enabled&&u&&u.classList.add(...h.lockClass.split(" "))};y.forEach(u=>d(u,"next")),w.forEach(u=>d(u,"prev"))}function S(){let{nextEl:h,prevEl:y}=e.navigation;h=s(h),y=s(y);const w=(d,u)=>{d.removeEventListener("click",u==="next"?f:c),d.classList.remove(...e.params.navigation.disabledClass.split(" "))};h.forEach(d=>w(d,"next")),y.forEach(d=>w(d,"prev"))}n("init",()=>{e.params.navigation.enabled===!1?b():(p(),r())}),n("toEdge fromEdge lock unlock",()=>{r()}),n("destroy",()=>{S()}),n("enable disable",()=>{let{nextEl:h,prevEl:y}=e.navigation;if(h=s(h),y=s(y),e.enabled){r();return}[...h,...y].filter(w=>!!w).forEach(w=>w.classList.add(e.params.navigation.lockClass))}),n("click",(h,y)=>{let{nextEl:w,prevEl:d}=e.navigation;w=s(w),d=s(d);const u=y.target;if(e.params.navigation.hideOnClick&&!d.includes(u)&&!w.includes(u)){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===u||e.pagination.el.contains(u)))return;let v;w.length?v=w[0].classList.contains(e.params.navigation.hiddenClass):d.length&&(v=d[0].classList.contains(e.params.navigation.hiddenClass)),a(v===!0?"navigationShow":"navigationHide"),[...w,...d].filter(x=>!!x).forEach(x=>x.classList.toggle(e.params.navigation.hiddenClass))}});const m=()=>{e.el.classList.remove(...e.params.navigation.navigationDisabledClass.split(" ")),p(),r()},b=()=>{e.el.classList.add(...e.params.navigation.navigationDisabledClass.split(" ")),S()};Object.assign(e.navigation,{enable:m,disable:b,update:r,init:p,destroy:S})}function Y(t){return t===void 0&&(t=""),`.${t.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function hi(t){let{swiper:e,extendParams:i,on:n,emit:a}=t;const s="swiper-pagination";i({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:d=>d,formatFractionTotal:d=>d,bulletClass:`${s}-bullet`,bulletActiveClass:`${s}-bullet-active`,modifierClass:`${s}-`,currentClass:`${s}-current`,totalClass:`${s}-total`,hiddenClass:`${s}-hidden`,progressbarFillClass:`${s}-progressbar-fill`,progressbarOppositeClass:`${s}-progressbar-opposite`,clickableClass:`${s}-clickable`,lockClass:`${s}-lock`,horizontalClass:`${s}-horizontal`,verticalClass:`${s}-vertical`,paginationDisabledClass:`${s}-disabled`}}),e.pagination={el:null,bullets:[]};let o,l=0;const r=d=>(Array.isArray(d)?d:[d]).filter(u=>!!u);function c(){return!e.params.pagination.el||!e.pagination.el||Array.isArray(e.pagination.el)&&e.pagination.el.length===0}function f(d,u){const{bulletActiveClass:v}=e.params.pagination;d&&(d=d[`${u==="prev"?"previous":"next"}ElementSibling`],d&&(d.classList.add(`${v}-${u}`),d=d[`${u==="prev"?"previous":"next"}ElementSibling`],d&&d.classList.add(`${v}-${u}-${u}`)))}function p(d){const u=d.target.closest(Y(e.params.pagination.bulletClass));if(!u)return;d.preventDefault();const v=J(u)*e.params.slidesPerGroup;if(e.params.loop){if(e.realIndex===v)return;const x=e.realIndex,L=e.getSlideIndexByData(v),E=e.getSlideIndexByData(e.realIndex),M=g=>{const O=e.activeIndex;e.loopFix({direction:g,activeSlideIndex:L,slideTo:!1});const T=e.activeIndex;O===T&&e.slideToLoop(x,0,!1,!0)};if(L>e.slides.length-e.loopedSlides)M(L>E?"next":"prev");else if(e.params.centeredSlides){const g=e.params.slidesPerView==="auto"?e.slidesPerViewDynamic():Math.ceil(parseFloat(e.params.slidesPerView,10));L<Math.floor(g/2)&&M("prev")}e.slideToLoop(v)}else e.slideTo(v)}function S(){const d=e.rtl,u=e.params.pagination;if(c())return;let v=e.pagination.el;v=r(v);let x,L;const E=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,M=e.params.loop?Math.ceil(E/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(L=e.previousRealIndex||0,x=e.params.slidesPerGroup>1?Math.floor(e.realIndex/e.params.slidesPerGroup):e.realIndex):typeof e.snapIndex<"u"?(x=e.snapIndex,L=e.previousSnapIndex):(L=e.previousIndex||0,x=e.activeIndex||0),u.type==="bullets"&&e.pagination.bullets&&e.pagination.bullets.length>0){const g=e.pagination.bullets;let O,T,C;if(u.dynamicBullets&&(o=oe(g[0],e.isHorizontal()?"width":"height",!0),v.forEach(P=>{P.style[e.isHorizontal()?"width":"height"]=`${o*(u.dynamicMainBullets+4)}px`}),u.dynamicMainBullets>1&&L!==void 0&&(l+=x-(L||0),l>u.dynamicMainBullets-1?l=u.dynamicMainBullets-1:l<0&&(l=0)),O=Math.max(x-l,0),T=O+(Math.min(g.length,u.dynamicMainBullets)-1),C=(T+O)/2),g.forEach(P=>{const I=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(A=>`${u.bulletActiveClass}${A}`)].map(A=>typeof A=="string"&&A.includes(" ")?A.split(" "):A).flat();P.classList.remove(...I)}),v.length>1)g.forEach(P=>{const I=J(P);I===x?P.classList.add(...u.bulletActiveClass.split(" ")):e.isElement&&P.setAttribute("part","bullet"),u.dynamicBullets&&(I>=O&&I<=T&&P.classList.add(...`${u.bulletActiveClass}-main`.split(" ")),I===O&&f(P,"prev"),I===T&&f(P,"next"))});else{const P=g[x];if(P&&P.classList.add(...u.bulletActiveClass.split(" ")),e.isElement&&g.forEach((I,A)=>{I.setAttribute("part",A===x?"bullet-active":"bullet")}),u.dynamicBullets){const I=g[O],A=g[T];for(let N=O;N<=T;N+=1)g[N]&&g[N].classList.add(...`${u.bulletActiveClass}-main`.split(" "));f(I,"prev"),f(A,"next")}}if(u.dynamicBullets){const P=Math.min(g.length,u.dynamicMainBullets+4),I=(o*P-o)/2-C*o,A=d?"right":"left";g.forEach(N=>{N.style[e.isHorizontal()?A:"top"]=`${I}px`})}}v.forEach((g,O)=>{if(u.type==="fraction"&&(g.querySelectorAll(Y(u.currentClass)).forEach(T=>{T.textContent=u.formatFractionCurrent(x+1)}),g.querySelectorAll(Y(u.totalClass)).forEach(T=>{T.textContent=u.formatFractionTotal(M)})),u.type==="progressbar"){let T;u.progressbarOpposite?T=e.isHorizontal()?"vertical":"horizontal":T=e.isHorizontal()?"horizontal":"vertical";const C=(x+1)/M;let P=1,I=1;T==="horizontal"?P=C:I=C,g.querySelectorAll(Y(u.progressbarFillClass)).forEach(A=>{A.style.transform=`translate3d(0,0,0) scaleX(${P}) scaleY(${I})`,A.style.transitionDuration=`${e.params.speed}ms`})}u.type==="custom"&&u.renderCustom?(g.innerHTML=u.renderCustom(e,x+1,M),O===0&&a("paginationRender",g)):(O===0&&a("paginationRender",g),a("paginationUpdate",g)),e.params.watchOverflow&&e.enabled&&g.classList[e.isLocked?"add":"remove"](u.lockClass)})}function m(){const d=e.params.pagination;if(c())return;const u=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length;let v=e.pagination.el;v=r(v);let x="";if(d.type==="bullets"){let L=e.params.loop?Math.ceil(u/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&&e.params.freeMode.enabled&&L>u&&(L=u);for(let E=0;E<L;E+=1)d.renderBullet?x+=d.renderBullet.call(e,E,d.bulletClass):x+=`<${d.bulletElement} ${e.isElement?'part="bullet"':""} class="${d.bulletClass}"></${d.bulletElement}>`}d.type==="fraction"&&(d.renderFraction?x=d.renderFraction.call(e,d.currentClass,d.totalClass):x=`<span class="${d.currentClass}"></span> / <span class="${d.totalClass}"></span>`),d.type==="progressbar"&&(d.renderProgressbar?x=d.renderProgressbar.call(e,d.progressbarFillClass):x=`<span class="${d.progressbarFillClass}"></span>`),e.pagination.bullets=[],v.forEach(L=>{d.type!=="custom"&&(L.innerHTML=x||""),d.type==="bullets"&&e.pagination.bullets.push(...L.querySelectorAll(Y(d.bulletClass)))}),d.type!=="custom"&&a("paginationRender",v[0])}function b(){e.params.pagination=De(e,e.originalParams.pagination,e.params.pagination,{el:"swiper-pagination"});const d=e.params.pagination;if(!d.el)return;let u;typeof d.el=="string"&&e.isElement&&(u=e.el.querySelector(d.el)),!u&&typeof d.el=="string"&&(u=[...document.querySelectorAll(d.el)]),u||(u=d.el),!(!u||u.length===0)&&(e.params.uniqueNavElements&&typeof d.el=="string"&&Array.isArray(u)&&u.length>1&&(u=[...e.el.querySelectorAll(d.el)],u.length>1&&(u=u.filter(v=>Ce(v,".swiper")[0]===e.el)[0])),Array.isArray(u)&&u.length===1&&(u=u[0]),Object.assign(e.pagination,{el:u}),u=r(u),u.forEach(v=>{d.type==="bullets"&&d.clickable&&v.classList.add(...(d.clickableClass||"").split(" ")),v.classList.add(d.modifierClass+d.type),v.classList.add(e.isHorizontal()?d.horizontalClass:d.verticalClass),d.type==="bullets"&&d.dynamicBullets&&(v.classList.add(`${d.modifierClass}${d.type}-dynamic`),l=0,d.dynamicMainBullets<1&&(d.dynamicMainBullets=1)),d.type==="progressbar"&&d.progressbarOpposite&&v.classList.add(d.progressbarOppositeClass),d.clickable&&v.addEventListener("click",p),e.enabled||v.classList.add(d.lockClass)}))}function h(){const d=e.params.pagination;if(c())return;let u=e.pagination.el;u&&(u=r(u),u.forEach(v=>{v.classList.remove(d.hiddenClass),v.classList.remove(d.modifierClass+d.type),v.classList.remove(e.isHorizontal()?d.horizontalClass:d.verticalClass),d.clickable&&(v.classList.remove(...(d.clickableClass||"").split(" ")),v.removeEventListener("click",p))})),e.pagination.bullets&&e.pagination.bullets.forEach(v=>v.classList.remove(...d.bulletActiveClass.split(" ")))}n("changeDirection",()=>{if(!e.pagination||!e.pagination.el)return;const d=e.params.pagination;let{el:u}=e.pagination;u=r(u),u.forEach(v=>{v.classList.remove(d.horizontalClass,d.verticalClass),v.classList.add(e.isHorizontal()?d.horizontalClass:d.verticalClass)})}),n("init",()=>{e.params.pagination.enabled===!1?w():(b(),m(),S())}),n("activeIndexChange",()=>{typeof e.snapIndex>"u"&&S()}),n("snapIndexChange",()=>{S()}),n("snapGridLengthChange",()=>{m(),S()}),n("destroy",()=>{h()}),n("enable disable",()=>{let{el:d}=e.pagination;d&&(d=r(d),d.forEach(u=>u.classList[e.enabled?"remove":"add"](e.params.pagination.lockClass)))}),n("lock unlock",()=>{S()}),n("click",(d,u)=>{const v=u.target,x=r(e.pagination.el);if(e.params.pagination.el&&e.params.pagination.hideOnClick&&x&&x.length>0&&!v.classList.contains(e.params.pagination.bulletClass)){if(e.navigation&&(e.navigation.nextEl&&v===e.navigation.nextEl||e.navigation.prevEl&&v===e.navigation.prevEl))return;const L=x[0].classList.contains(e.params.pagination.hiddenClass);a(L===!0?"paginationShow":"paginationHide"),x.forEach(E=>E.classList.toggle(e.params.pagination.hiddenClass))}});const y=()=>{e.el.classList.remove(e.params.pagination.paginationDisabledClass);let{el:d}=e.pagination;d&&(d=r(d),d.forEach(u=>u.classList.remove(e.params.pagination.paginationDisabledClass))),b(),m(),S()},w=()=>{e.el.classList.add(e.params.pagination.paginationDisabledClass);let{el:d}=e.pagination;d&&(d=r(d),d.forEach(u=>u.classList.add(e.params.pagination.paginationDisabledClass))),h()};Object.assign(e.pagination,{enable:y,disable:w,render:m,update:S,init:b,destroy:h})}function gi(t){let{swiper:e,extendParams:i,on:n,emit:a,params:s}=t;e.autoplay={running:!1,paused:!1,timeLeft:0},i({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let o,l,r=s&&s.autoplay?s.autoplay.delay:3e3,c=s&&s.autoplay?s.autoplay.delay:3e3,f,p=new Date().getTime,S,m,b,h,y,w;function d(z){!e||e.destroyed||!e.wrapperEl||z.target===e.wrapperEl&&(e.wrapperEl.removeEventListener("transitionend",d),g())}const u=()=>{if(e.destroyed||!e.autoplay.running)return;e.autoplay.paused?S=!0:S&&(c=f,S=!1);const z=e.autoplay.paused?f:p+c-new Date().getTime();e.autoplay.timeLeft=z,a("autoplayTimeLeft",z,z/r),l=requestAnimationFrame(()=>{u()})},v=()=>{let z;return e.virtual&&e.params.virtual.enabled?z=e.slides.filter(B=>B.classList.contains("swiper-slide-active"))[0]:z=e.slides[e.activeIndex],z?parseInt(z.getAttribute("data-swiper-autoplay"),10):void 0},x=z=>{if(e.destroyed||!e.autoplay.running)return;cancelAnimationFrame(l),u();let $=typeof z>"u"?e.params.autoplay.delay:z;r=e.params.autoplay.delay,c=e.params.autoplay.delay;const B=v();!Number.isNaN(B)&&B>0&&typeof z>"u"&&($=B,r=B,c=B),f=$;const H=e.params.speed,X=()=>{!e||e.destroyed||(e.params.autoplay.reverseDirection?!e.isBeginning||e.params.loop||e.params.rewind?(e.slidePrev(H,!0,!0),a("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(e.slides.length-1,H,!0,!0),a("autoplay")):!e.isEnd||e.params.loop||e.params.rewind?(e.slideNext(H,!0,!0),a("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(0,H,!0,!0),a("autoplay")),e.params.cssMode&&(p=new Date().getTime(),requestAnimationFrame(()=>{x()})))};return $>0?(clearTimeout(o),o=setTimeout(()=>{X()},$)):requestAnimationFrame(()=>{X()}),$},L=()=>{e.autoplay.running=!0,x(),a("autoplayStart")},E=()=>{e.autoplay.running=!1,clearTimeout(o),cancelAnimationFrame(l),a("autoplayStop")},M=(z,$)=>{if(e.destroyed||!e.autoplay.running)return;clearTimeout(o),z||(w=!0);const B=()=>{a("autoplayPause"),e.params.autoplay.waitForTransition?e.wrapperEl.addEventListener("transitionend",d):g()};if(e.autoplay.paused=!0,$){y&&(f=e.params.autoplay.delay),y=!1,B();return}f=(f||e.params.autoplay.delay)-(new Date().getTime()-p),!(e.isEnd&&f<0&&!e.params.loop)&&(f<0&&(f=0),B())},g=()=>{e.isEnd&&f<0&&!e.params.loop||e.destroyed||!e.autoplay.running||(p=new Date().getTime(),w?(w=!1,x(f)):x(),e.autoplay.paused=!1,a("autoplayResume"))},O=()=>{if(e.destroyed||!e.autoplay.running)return;const z=k();z.visibilityState==="hidden"&&(w=!0,M(!0)),z.visibilityState==="visible"&&g()},T=z=>{z.pointerType==="mouse"&&(w=!0,!(e.animating||e.autoplay.paused)&&M(!0))},C=z=>{z.pointerType==="mouse"&&e.autoplay.paused&&g()},P=()=>{e.params.autoplay.pauseOnMouseEnter&&(e.el.addEventListener("pointerenter",T),e.el.addEventListener("pointerleave",C))},I=()=>{e.el.removeEventListener("pointerenter",T),e.el.removeEventListener("pointerleave",C)},A=()=>{k().addEventListener("visibilitychange",O)},N=()=>{k().removeEventListener("visibilitychange",O)};n("init",()=>{e.params.autoplay.enabled&&(P(),A(),p=new Date().getTime(),L())}),n("destroy",()=>{I(),N(),e.autoplay.running&&E()}),n("beforeTransitionStart",(z,$,B)=>{e.destroyed||!e.autoplay.running||(B||!e.params.autoplay.disableOnInteraction?M(!0,!0):E())}),n("sliderFirstMove",()=>{if(!(e.destroyed||!e.autoplay.running)){if(e.params.autoplay.disableOnInteraction){E();return}m=!0,b=!1,w=!1,h=setTimeout(()=>{w=!0,b=!0,M(!0)},200)}}),n("touchEnd",()=>{if(!(e.destroyed||!e.autoplay.running||!m)){if(clearTimeout(h),clearTimeout(o),e.params.autoplay.disableOnInteraction){b=!1,m=!1;return}b&&e.params.cssMode&&g(),b=!1,m=!1}}),n("slideChange",()=>{e.destroyed||!e.autoplay.running||(y=!0)}),Object.assign(e.autoplay,{start:L,stop:E,pause:M,resume:g})}export{gi as A,mi as N,hi as P,pi as S,ci as a};
