import{_ as B,r as p,c as x,P,o as I,e as u,f as g,g as t,z as r,j as h,F as y,i as C,t as c,C as T,R as V,p as A}from"./index-ad117935.js";const F={class:"pagination-wrapper"},D={class:"pagination-container flex flex-wrap justify-center lg:justify-start items-center space-x-1"},L=["disabled"],$=["disabled"],q={class:"page-numbers flex space-x-1"},E={key:0,class:"pagination-ellipsis"},O=["onClick","disabled"],R={key:1,class:"pagination-ellipsis"},U=["disabled"],G=["disabled"],H={class:"page-size-selector flex items-center justify-center lg:justify-end"},J=["disabled"],K=["value"],Q={key:0,class:"ml-3"},W={class:"pagination-info text-center text-sm text-gray-500 mt-4"},X={class:"ml-2"},Y={key:0,class:"ml-2 text-blue-600"},Z={__name:"PageItem",props:{totalPage:{type:Number,required:!0},initialPage:{type:Number,default:1},pageSize:{type:Number,default:20},loading:{type:Boolean,default:!1}},emits:["pageChange","pageSizeChange"],setup(n,{expose:k,emit:z}){const i=n,f=z,e=p(i.initialPage),s=p(i.pageSize),S=[10,20,50,100],m=x(()=>{const l=Math.max(1,Math.ceil(i.totalPage/s.value)),a=e.value;return l<=9?Array.from({length:l},(o,b)=>b+1):a<=5?[1,2,3,4,5,6,7,8,9]:a>=l-4?Array.from({length:9},(o,b)=>l-8+b):Array.from({length:9},(o,b)=>a-4+b)}),d=x(()=>Math.max(1,Math.ceil(i.totalPage/s.value))),v=l=>{l<1||l>d.value||l===e.value||i.loading||(e.value=l,f("pageChange",l))},w=()=>{e.value!==1&&!i.loading&&v(1)},M=()=>{e.value!==d.value&&!i.loading&&v(d.value)},N=()=>{e.value>1&&!i.loading&&v(e.value-1)},_=()=>{e.value<d.value&&!i.loading&&v(e.value+1)},j=l=>{i.loading||(s.value=l,e.value=1,f("pageSizeChange",l),f("pageChange",1))};return P(()=>i.totalPage,l=>{const a=Math.max(1,Math.ceil(l/s.value));e.value>a&&(e.value=1,f("pageChange",1))}),P(()=>i.pageSize,l=>{l!==s.value&&(s.value=l)}),I(()=>{i.initialPage!==e.value&&(e.value=i.initialPage),i.pageSize!==s.value&&(s.value=i.pageSize)}),k({nowPage:e,changePage:v,currentPageSize:s}),(l,a)=>(u(),g("div",F,[t("div",{class:r(["pagination-main-container flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",{loading:n.loading}])},[t("div",D,[t("button",{onClick:w,disabled:e.value===1||n.loading,class:r(["pagination-btn first-btn",{disabled:e.value===1||n.loading}]),title:"首頁"},a[2]||(a[2]=[t("i",{class:"fas fa-angle-double-left"},null,-1),t("span",{class:"hidden sm:inline ml-1"},"首頁",-1)]),10,L),t("button",{onClick:N,disabled:e.value===1||n.loading,class:r(["pagination-btn prev-btn",{disabled:e.value===1||n.loading}]),title:"上一頁"},a[3]||(a[3]=[t("i",{class:"fas fa-chevron-left"},null,-1),t("span",{class:"hidden sm:inline ml-1"},"上一頁",-1)]),10,$),t("div",q,[m.value[0]>1?(u(),g("span",E,"...")):h("",!0),(u(!0),g(y,null,C(m.value,o=>(u(),g("button",{key:o,onClick:b=>v(o),disabled:n.loading,class:r(["page-number-btn",{active:o===e.value,disabled:n.loading}])},c(o),11,O))),128)),m.value[m.value.length-1]<d.value?(u(),g("span",R,"...")):h("",!0)]),t("button",{onClick:_,disabled:e.value===d.value||n.loading,class:r(["pagination-btn next-btn",{disabled:e.value===d.value||n.loading}]),title:"下一頁"},a[4]||(a[4]=[t("span",{class:"hidden sm:inline mr-1"},"下一頁",-1),t("i",{class:"fas fa-chevron-right"},null,-1)]),10,U),t("button",{onClick:M,disabled:e.value===d.value||n.loading,class:r(["pagination-btn last-btn",{disabled:e.value===d.value||n.loading}]),title:"末頁"},a[5]||(a[5]=[t("span",{class:"hidden sm:inline mr-1"},"末頁",-1),t("i",{class:"fas fa-angle-double-right"},null,-1)]),10,G)]),t("div",H,[a[7]||(a[7]=t("span",{class:"text-sm text-gray-600 mr-2 whitespace-nowrap"},"每頁顯示：",-1)),T(t("select",{"onUpdate:modelValue":a[0]||(a[0]=o=>s.value=o),onChange:a[1]||(a[1]=o=>j(s.value)),disabled:n.loading,class:r(["page-size-select",{disabled:n.loading}])},[(u(),g(y,null,C(S,o=>t("option",{key:o,value:o},c(o)+" 項 ",9,K)),64))],42,J),[[V,s.value]]),n.loading?(u(),g("div",Q,a[6]||(a[6]=[t("div",{class:"inline-block w-4 h-4 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin"},null,-1)]))):h("",!0)])],2),t("div",W,[A(" 第 "+c(e.value)+" 頁，共 "+c(d.value)+" 頁，總計 "+c(n.totalPage)+" 項 ",1),t("span",X," （顯示第 "+c((e.value-1)*s.value+1)+" - "+c(Math.min(e.value*s.value,n.totalPage))+" 項） ",1),n.loading?(u(),g("span",Y,"載入中...")):h("",!0)])]))}},ae=B(Z,[["__scopeId","data-v-bbf97dde"]]);export{ae as P};
