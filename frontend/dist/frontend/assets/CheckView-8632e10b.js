import{_ as j,G as F,q as M,s as P,r as L,H as C,c as X,P as z,o as E,b as O,x as A,y as G,v as H,d as J,e as i,f as a,u as I,g as r,h as V,t as g,B as K,p,C as c,E as m,z as x,j as b,w as U,F as Q,L as W}from"./index-ad117935.js";const T=""+new URL("task-df33db08.svg",import.meta.url).href;const Y={key:0,class:"container m-auto px-6 xl:px-0 py-10"},Z={class:"grid grid-cols-1 xl:grid-cols-3 gap-10"},ee={class:"bg-gray-200 p-8 rounded-lg md:col-span-2"},re={class:"flex justify-end mt-4 border-t-2 border-gray-400 pt-4"},te={class:"text-2xl font-bold"},oe={class:"xl:mt-20"},se={class:"mb-4"},le={key:0,class:"text-red-500 text-sm mt-1"},de={class:"mb-4"},ie={key:0,class:"text-red-500 text-sm mt-1"},ae={class:"mb-4"},ne={key:0,class:"text-red-500 text-sm mt-1"},ue={class:"mb-4"},pe={key:0,class:"text-red-500 text-sm mt-1"},ve={key:1,class:"text-gray-500 text-sm mt-1"},ce={class:"mb-4"},me={key:0,class:"text-red-500 text-sm mt-1"},_e={class:"mb-4"},ge={key:0,class:"text-red-500 text-sm mt-1"},be={class:"mb-4"},fe={class:"mb-4"},xe={class:"mb-4 col-span-2"},he={class:"col-span-2"},ye={key:0,class:"text-red-500 text-center mb-2"},ke=["disabled"],we={key:1,class:"container m-auto py-10 text-center flex flex-col items-center",style:{"min-height":"50vh"}},$e={class:"mt-10"},Ve={key:2,class:"container m-auto text-center py-20"},Ie={class:"flex flex-col justify-center items-center"},Ue={class:"mt-10"},Se={__name:"CheckView",setup(Ce){const w=F(),D=M(),{clientData:h,carts:n,total:$,isUser:R}=P(D),f=L(!1),t=C({orderer:"",orderer_phone:"",receiver:"",receiver_phone:"",detailed_address:"",cooperative:"",invoice_title:"",tax_id:"",notes:""}),s=C({orderer:"",orderer_phone:"",receiver:"",receiver_phone:"",detailed_address:"",cooperative:"",invoice_title:"",tax_id:"",notes:""}),l=C({orderer:!1,orderer_phone:!1,receiver:!1,receiver_phone:!1,detailed_address:!1,cooperative:!1,invoice_title:!1,tax_id:!1,notes:!1}),u=L(""),B=d=>{if(!/^[0-9]+$/.test(d))return"電話號碼只能包含數字";const v=/^09\d{8}$/,o=/^0\d{1,2}\d{6,8}$/;return!v.test(d)&&!o.test(d)?"請輸入有效的台灣電話號碼格式":""},_=()=>{t.orderer.trim()?s.orderer="":s.orderer="訂購人資訊缺失",t.orderer_phone.trim()?s.orderer_phone=B(t.orderer_phone):s.orderer_phone="訂購人電話缺失",t.receiver.trim()?s.receiver="":s.receiver="請輸入收貨人姓名",t.receiver_phone.trim()?s.receiver_phone=B(t.receiver_phone):s.receiver_phone="請輸入收貨人電話",t.detailed_address.trim()?s.detailed_address="":s.detailed_address="請輸入詳細住址",t.cooperative.trim()?s.cooperative="":s.cooperative="請輸入儲互社資訊"},y=d=>{l[d]=!0,_()},N=d=>{const e=d.target.value;t.receiver_phone=e.replace(/\D/g,""),l.receiver_phone=!0,_()},S=X(()=>t.orderer.trim()!==""&&t.orderer_phone.trim()!==""&&t.receiver.trim()!==""&&t.receiver_phone.trim()!==""&&t.detailed_address.trim()!==""&&t.cooperative.trim()!==""&&s.orderer===""&&s.orderer_phone===""&&s.receiver===""&&s.receiver_phone===""&&s.detailed_address===""&&s.cooperative==="");z(n,d=>{(!d||d.length===0)&&!f.value&&(console.log("購物車已清空，導向首頁"),setTimeout(()=>{!f.value&&(!n.value||n.value.length===0)&&w.push("/")},100))},{deep:!0}),E(()=>{const d=localStorage.getItem("token"),e=localStorage.getItem("isLogin");if(!e||!d){localStorage.setItem("redirect_after_login",window.location.href),w.push("/login");return}if(e&&d&&!R.value&&(R.value=!0,console.log("已同步設置用戶登入狀態")),(!n.value||n.value.length===0)&&!f.value){console.log("購物車為空，導向首頁"),w.push("/");return}h.value&&(t.orderer=h.value.name||"",t.orderer_phone=h.value.phone||"",t.receiver=h.value.name||"",t.receiver_phone=h.value.phone||"",t.orderer&&(l.orderer=!0),t.orderer_phone&&(l.orderer_phone=!0),t.receiver&&(l.receiver=!0),t.receiver_phone&&(l.receiver_phone=!0),_())});const q=O.throttle(async()=>{var d,e,v;if(u.value="",l.orderer=!0,l.orderer_phone=!0,l.receiver=!0,l.receiver_phone=!0,l.detailed_address=!0,l.cooperative=!0,_(),!S.value){u.value="請填寫所有必填欄位並修正錯誤";return}if(!n.value||n.value.length===0){u.value="購物車是空的，請先添加商品",w.push("/");return}if(!$.value||$.value<=0){u.value="訂單總金額有誤，請重新整理頁面";return}try{const o={orderer:t.orderer,orderer_phone:t.orderer_phone,receiver:t.receiver,receiver_phone:t.receiver_phone,phone:t.receiver_phone,address:"自取/無須配送",detailed_address:t.detailed_address,cooperative:t.cooperative,invoice_title:t.invoice_title,tax_id:t.tax_id,notes:t.notes,products:[...n.value],total:$.value,client:((d=h.value)==null?void 0:d.id)||"1",status:1};console.log("送出訂單資料:",o);const k=await A.post(G("orderList"),o,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`}});console.log("訂單提交結果:",k.data),k.data&&(k.data.success||k.status==200||k.status==201)?(f.value=!0,n.value=[],$.value=0,localStorage.setItem("carts",JSON.stringify([])),H.success("訂單提交成功！"),setTimeout(()=>{f.value&&w.push("/account")},3e3)):u.value=((e=k.data)==null?void 0:e.message)||"訂單提交失敗，請稍後再試"}catch(o){console.error("訂單提交錯誤:",o),o.response?u.value=`訂單提交失敗：${o.response.status} - ${((v=o.response.data)==null?void 0:v.message)||"伺服器錯誤"}`:o.request?u.value="無法連接到伺服器，請檢查網路連接並稍後再試":u.value=`訂單提交過程中發生錯誤：${o.message}`}},2e3,{trailing:!1});return(d,e)=>{const v=J("RouterLink");return i(),a(Q,null,[I(n).length>0?(i(),a("div",Y,[r("div",Z,[r("div",ee,[e[20]||(e[20]=r("h3",{class:"text-3xl font-bold mb-6"},"購物明細",-1)),V(W),r("div",re,[r("p",te,"訂單總金額: NT$ "+g(I($).toLocaleString()),1)])]),r("div",oe,[e[30]||(e[30]=r("h4",{class:"text-3xl font-bold"},"訂購資料",-1)),e[31]||(e[31]=r("p",{class:"text-gray-600 mb-4"},null,-1)),r("form",{onSubmit:e[19]||(e[19]=K((...o)=>I(q)&&I(q)(...o),["prevent"])),class:"grid grid-cols-2 gap-4 mt-4"},[r("div",se,[e[21]||(e[21]=r("label",{for:"orderer",class:"block mb-1"},[p(" 訂購人 "),r("span",{class:"text-red-500"},"*"),r("span",{class:"text-gray-500 text-sm"},"(來自個人資料)")],-1)),c(r("input",{id:"orderer",class:x(["w-full border-2 p-2 rounded bg-gray-100",{"border-red-500":l.orderer&&s.orderer}]),placeholder:"訂購人姓名","onUpdate:modelValue":e[0]||(e[0]=o=>t.orderer=o),readonly:"",required:""},null,2),[[m,t.orderer]]),l.orderer&&s.orderer?(i(),a("p",le,g(s.orderer),1)):b("",!0)]),r("div",de,[e[22]||(e[22]=r("label",{for:"orderer_phone",class:"block mb-1"},[p(" 訂購人電話 "),r("span",{class:"text-red-500"},"*"),r("span",{class:"text-gray-500 text-sm"},"(來自個人資料)")],-1)),c(r("input",{id:"orderer_phone",class:x(["w-full border-2 p-2 rounded bg-gray-100",{"border-red-500":l.orderer_phone&&s.orderer_phone}]),placeholder:"訂購人電話","onUpdate:modelValue":e[1]||(e[1]=o=>t.orderer_phone=o),readonly:"",required:""},null,2),[[m,t.orderer_phone]]),l.orderer_phone&&s.orderer_phone?(i(),a("p",ie,g(s.orderer_phone),1)):b("",!0)]),r("div",ae,[e[23]||(e[23]=r("label",{for:"receiver",class:"block mb-1"},[p(" 收貨人 "),r("span",{class:"text-red-500"},"*")],-1)),c(r("input",{id:"receiver",class:x(["w-full border-2 p-2 rounded",{"border-red-500":l.receiver&&s.receiver}]),placeholder:"請輸入收貨人姓名","onUpdate:modelValue":e[2]||(e[2]=o=>t.receiver=o),onInput:e[3]||(e[3]=o=>y("receiver")),onBlur:e[4]||(e[4]=o=>{l.receiver=!0,_()}),required:""},null,34),[[m,t.receiver]]),l.receiver&&s.receiver?(i(),a("p",ne,g(s.receiver),1)):b("",!0)]),r("div",ue,[e[24]||(e[24]=r("label",{for:"receiver_phone",class:"block mb-1"},[p(" 收貨人電話 "),r("span",{class:"text-red-500"},"*")],-1)),c(r("input",{id:"receiver_phone",class:x(["w-full border-2 p-2 rounded",{"border-red-500":l.receiver_phone&&s.receiver_phone}]),placeholder:"請輸入收貨人電話 (僅數字)","onUpdate:modelValue":e[5]||(e[5]=o=>t.receiver_phone=o),onInput:N,onBlur:e[6]||(e[6]=o=>{l.receiver_phone=!0,_()}),required:""},null,34),[[m,t.receiver_phone]]),l.receiver_phone&&s.receiver_phone?(i(),a("p",pe,g(s.receiver_phone),1)):(i(),a("p",ve," 請輸入有效的台灣手機或市話號碼 "))]),r("div",ce,[e[25]||(e[25]=r("label",{for:"detailed_address",class:"block mb-1"},[p(" 住址（詳細地址） "),r("span",{class:"text-red-500"},"*")],-1)),c(r("input",{id:"detailed_address",class:x(["w-full border-2 p-2 rounded",{"border-red-500":l.detailed_address&&s.detailed_address}]),placeholder:"請輸入詳細住址","onUpdate:modelValue":e[7]||(e[7]=o=>t.detailed_address=o),onInput:e[8]||(e[8]=o=>y("detailed_address")),onBlur:e[9]||(e[9]=o=>{l.detailed_address=!0,_()}),required:""},null,34),[[m,t.detailed_address]]),l.detailed_address&&s.detailed_address?(i(),a("p",me,g(s.detailed_address),1)):b("",!0)]),r("div",_e,[e[26]||(e[26]=r("label",{for:"cooperative",class:"block mb-1"},[p(" 儲互社（XX區XX社） "),r("span",{class:"text-red-500"},"*")],-1)),c(r("input",{id:"cooperative",class:x(["w-full border-2 p-2 rounded",{"border-red-500":l.cooperative&&s.cooperative}]),placeholder:"請輸入儲互社資訊","onUpdate:modelValue":e[10]||(e[10]=o=>t.cooperative=o),onInput:e[11]||(e[11]=o=>y("cooperative")),onBlur:e[12]||(e[12]=o=>{l.cooperative=!0,_()}),required:""},null,34),[[m,t.cooperative]]),l.cooperative&&s.cooperative?(i(),a("p",ge,g(s.cooperative),1)):b("",!0)]),r("div",be,[e[27]||(e[27]=r("label",{for:"invoice_title",class:"block mb-1"}," 公司抬頭 ",-1)),c(r("input",{id:"invoice_title",class:"w-full border-2 p-2 rounded",placeholder:"可選填","onUpdate:modelValue":e[13]||(e[13]=o=>t.invoice_title=o),onInput:e[14]||(e[14]=o=>y("invoice_title"))},null,544),[[m,t.invoice_title]])]),r("div",fe,[e[28]||(e[28]=r("label",{for:"tax_id",class:"block mb-1"}," 統編 ",-1)),c(r("input",{id:"tax_id",class:"w-full border-2 p-2 rounded",placeholder:"可選填","onUpdate:modelValue":e[15]||(e[15]=o=>t.tax_id=o),onInput:e[16]||(e[16]=o=>y("tax_id"))},null,544),[[m,t.tax_id]])]),r("div",xe,[e[29]||(e[29]=r("label",{for:"notes",class:"block mb-1"}," 備註 ",-1)),c(r("textarea",{id:"notes",class:"w-full border-2 p-2 rounded",placeholder:"可選填",rows:"3","onUpdate:modelValue":e[17]||(e[17]=o=>t.notes=o),onInput:e[18]||(e[18]=o=>y("notes"))},null,544),[[m,t.notes]])]),r("div",he,[u.value?(i(),a("p",ye,g(u.value),1)):b("",!0),r("button",{type:"submit",class:x(["bg-black text-white w-full p-3 rounded-lg hover:bg-gray-800 transition mt-4",{"opacity-50 cursor-not-allowed":!S.value}]),disabled:!S.value}," 送出訂單 ",10,ke)])],32)])])])):b("",!0),f.value?(i(),a("div",we,[e[34]||(e[34]=r("img",{src:T,class:"w-20",alt:"menu"},null,-1)),e[35]||(e[35]=r("p",{class:"text-2xl mt-4 mb-6"},"成功送出訂單",-1)),e[36]||(e[36]=r("p",{class:"text-gray-600 mb-4"},"您的訂單已成功提交，請到店自取商品",-1)),r("div",$e,[V(v,{to:"/",class:"mr-4 bg-gray-800 text-white px-6 py-2 rounded-lg hover:bg-black"},{default:U(()=>e[32]||(e[32]=[p(" 返回首頁 ")])),_:1,__:[32]}),V(v,{to:"/account",class:"bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"},{default:U(()=>e[33]||(e[33]=[p(" 查看訂單 ")])),_:1,__:[33]})])])):f.value?(i(),a("div",Ve,[r("div",Ie,[e[39]||(e[39]=r("img",{src:T,class:"w-20",alt:"menu"},null,-1)),e[40]||(e[40]=r("p",{class:"text-2xl mt-4 mb-6"},"成功送出訂單",-1)),e[41]||(e[41]=r("p",{class:"text-gray-600 mb-4"},"您的訂單已成功提交，請到店自取商品",-1)),r("div",Ue,[V(v,{to:"/",class:"mr-4 bg-gray-800 text-white px-6 py-2 rounded-lg hover:bg-black"},{default:U(()=>e[37]||(e[37]=[p(" 返回首頁 ")])),_:1,__:[37]}),V(v,{to:"/account",class:"bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"},{default:U(()=>e[38]||(e[38]=[p(" 查看訂單 ")])),_:1,__:[38]})])])])):b("",!0)],64)}}},Be=j(Se,[["__scopeId","data-v-2895d9d2"]]);export{Be as default};
