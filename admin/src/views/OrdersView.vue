<template>
  <div class="orders-admin">
    <div class="mb-6 flex justify-end items-center">
      <div class="flex space-x-2" v-if="selectedOrders.length > 0">
        <span class="self-center mr-2 text-sm text-gray-600"
          >已選擇 {{ selectedOrders.length }} 筆訂單</span
        >
        <button @click="showBatchStatusModal" class="btn-blue-sm">
          <i class="fas fa-edit mr-1"></i> 批量更新狀態
        </button>
        <button @click="showBatchRemittanceDateModalFunc" class="btn-green-sm">
          <i class="fas fa-calendar-alt mr-1"></i> 批量設定匯款日期
        </button>
        <button @click="showBatchDeleteConfirm" class="btn-red-sm">
          <i class="fas fa-trash-alt mr-1"></i> 批量刪除
        </button>
      </div>
    </div>

    <!-- 搜尋與過濾區域 -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">訂單編號</label>
          <input
            type="text"
            v-model="filters.orderId"
            placeholder="搜尋訂單編號"
            class="form-input"
            @input="debounceSearch"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">客戶姓名</label>
          <input
            type="text"
            v-model="filters.client"
            placeholder="搜尋客戶姓名"
            class="form-input"
            @input="debounceSearch"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">訂單狀態</label>
          <select v-model="filters.status" class="form-select" @change="loadOrders">
            <option value="">全部狀態</option>
            <option value="1">處理中</option>
            <option value="2">已出貨</option>
            <option value="3">已完成</option>
            <option value="4">已取消</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 訂單列表 -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div v-if="loading" class="p-6 text-center">
        <div class="spinner"></div>
        <p class="mt-2 text-gray-600">載入中...</p>
      </div>
      <div v-else-if="orders.length === 0" class="p-6 text-center text-gray-500">
        <i class="fas fa-shopping-cart text-5xl mb-4"></i>
        <p>暫無訂單資料</p>
      </div>
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="th-checkbox">
                  <input
                    type="checkbox"
                    class="form-checkbox rounded"
                    :checked="selectAll"
                    @change="toggleSelectAll"
                  />
                </th>
                <th class="th">訂單編號</th>
                <th class="th">客戶姓名</th>
                <th class="th">訂購日期</th>
                <th class="th">訂單金額</th>
                <th class="th">訂購人</th>
                <th class="th">聯絡電話</th>
                <th class="th">付款狀態</th>
                <th class="th">匯款日期</th>
                <th class="th">訂單狀態</th>
                <th class="th">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="order in orders" :key="order.id" class="hover:bg-gray-50">
                <td class="px-4 py-4 text-center">
                  <input
                    type="checkbox"
                    class="form-checkbox rounded"
                    v-model="selectedOrders"
                    :value="order.id"
                  />
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ order.orderId }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ order.clientName }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(order.createdAt) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  NT$ {{ formatNumber(order.total) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ order.receiver }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ order.phone }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getPaymentStatusClass(order.payment_status)">
                    {{ getPaymentStatusText(order.payment_status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div class="flex items-center space-x-2">
                    <span v-if="order.remittance_date">
                      {{ formatRemittanceDate(order.remittance_date) }}
                    </span>
                    <span v-else class="text-gray-400">未設定</span>
                    <button
                      @click="showUpdateRemittanceDate(order)"
                      class="text-blue-600 hover:text-blue-800 text-xs"
                      title="設定匯款日期"
                    >
                      <i class="fas fa-calendar-alt"></i>
                    </button>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getStatusClass(order.status)">
                    {{ getStatusText(order.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div class="flex items-center space-x-1">
                    <button
                      @click="showOrderDetail(order)"
                      class="action-btn view"
                      title="查看詳情"
                    >
                      <i class="fas fa-eye"></i>
                    </button>
                    <button
                      @click="showUpdatePaymentStatus(order)"
                      class="action-btn payment"
                      title="更新付款狀態"
                    >
                      <i class="fas fa-money-check-alt"></i>
                    </button>
                    <button
                      @click="showUpdateStatus(order)"
                      class="action-btn edit"
                      title="更新訂單狀態"
                    >
                      <i class="fas fa-edit"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <!-- 分頁 -->
        <div class="px-6 py-3 flex justify-between items-center border-t">
          <div class="text-sm text-gray-500">
            顯示 {{ pagination.start }}-{{ pagination.end }} 筆，共 {{ pagination.total }} 筆
          </div>
          <div class="flex items-center space-x-2">
            <button
              @click="changePage(pagination.current - 1)"
              :disabled="pagination.current === 1"
              class="btn-gray-sm"
              :class="{ 'opacity-50 cursor-not-allowed': pagination.current === 1 }"
            >
              <i class="fas fa-chevron-left"></i>
            </button>
            <span
              v-for="page in pageNumbers"
              :key="page"
              class="page-number"
              :class="{ 'bg-blue-600 text-white': page === pagination.current }"
              @click="changePage(page)"
            >
              {{ page }}
            </span>
            <button
              @click="changePage(pagination.current + 1)"
              :disabled="pagination.current === pagination.totalPages"
              class="btn-gray-sm"
              :class="{
                'opacity-50 cursor-not-allowed': pagination.current === pagination.totalPages,
              }"
            >
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 訂單詳情彈窗 -->
    <div
      v-if="showDetailModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      @click.self="showDetailModal = false"
    >
      <div class="bg-white rounded-lg shadow-lg w-full max-w-3xl p-6 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-4 sticky top-0 bg-white z-10 pb-2">
          <h3 class="text-xl font-semibold">訂單詳情</h3>
          <button @click="showDetailModal = false" class="text-gray-500 hover:text-gray-700 p-2">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>

        <div v-if="selectedOrder" class="space-y-6">
          <!-- 訂單基本資訊 -->
          <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div class="info-group">
              <label class="info-label">訂單編號</label>
              <div class="info-value">{{ selectedOrder.orderId }}</div>
            </div>
            <div class="info-group">
              <label class="info-label">訂購日期</label>
              <div class="info-value">{{ formatDate(selectedOrder.createdAt) }}</div>
            </div>
            <div class="info-group">
              <label class="info-label">客戶姓名</label>
              <div class="info-value">{{ selectedOrder.clientName }}</div>
            </div>
            <div class="info-group">
              <label class="info-label">訂單狀態</label>
              <div class="info-value">
                <span :class="getStatusClass(selectedOrder.status)">
                  {{ getStatusText(selectedOrder.status) }}
                </span>
              </div>
            </div>
            <div class="info-group">
              <label class="info-label">付款狀態</label>
              <div class="info-value">
                <span :class="getPaymentStatusClass(selectedOrder.payment_status)">
                  {{ getPaymentStatusText(selectedOrder.payment_status) }}
                  <span v-if="selectedOrder.payment_date" class="text-xs text-gray-500 ml-2">
                    ({{ formatDate(selectedOrder.payment_date) }})
                  </span>
                </span>
              </div>
            </div>
            <div class="info-group">
              <label class="info-label">匯款日期</label>
              <div class="info-value">
                <span v-if="selectedOrder.remittance_date">
                  {{ formatRemittanceDate(selectedOrder.remittance_date) }}
                </span>
                <span v-else class="text-gray-400">未設定</span>
              </div>
            </div>
            <div class="info-group">
              <label class="info-label">訂購人</label>
              <div class="info-value">{{ selectedOrder.orderer || selectedOrder.receiver || '未提供' }}</div>
            </div>
            <div class="info-group">
              <label class="info-label">訂購人電話</label>
              <div class="info-value">{{ selectedOrder.orderer_phone || selectedOrder.phone || '未提供' }}</div>
            </div>
            <div class="info-group"></div>
            <div class="info-group">
              <label class="info-label">收貨人</label>
              <div class="info-value">{{ selectedOrder.receiver || '未提供' }}</div>
            </div>
            <div class="info-group">
              <label class="info-label">收貨人電話</label>
              <div class="info-value">{{ selectedOrder.receiver_phone || selectedOrder.phone || '未提供' }}</div>
            </div>
            <div class="w-full col-span-2 md:col-span-3"></div>
            <div class="info-group">
              <label class="info-label">住址（詳細地址）</label>
              <div class="info-value">{{ selectedOrder.detailed_address && selectedOrder.detailed_address !== '/' ? selectedOrder.detailed_address : '未提供' }}</div>
            </div>
            <div class="info-group">
              <label class="info-label">儲互社</label>
              <div class="info-value">{{ selectedOrder.cooperative && selectedOrder.cooperative !== '/' ? selectedOrder.cooperative : '未提供' }}</div>
            </div>
            <div class="w-full col-span-2 md:col-span-3"></div>
            <div class="info-group">
              <label class="info-label">抬頭</label>
              <div class="info-value">{{ selectedOrder.invoice_title && selectedOrder.invoice_title !== '/' ? selectedOrder.invoice_title : '未提供' }}</div>
            </div>
            <div class="info-group">
              <label class="info-label">統編</label>
              <div class="info-value">{{ selectedOrder.tax_id && selectedOrder.tax_id !== '/' ? selectedOrder.tax_id : '未提供' }}</div>
            </div>
            <div class="info-group col-span-2 md:col-span-3">
              <label class="info-label">備註</label>
              <div class="info-value">{{ selectedOrder.notes && selectedOrder.notes !== '/' ? selectedOrder.notes : '無' }}</div>
            </div>
          </div>

          <!-- 訂單商品 -->
          <div>
            <h4 class="text-lg font-medium mb-2">訂購商品</h4>
            
            <!-- 數量提示訊息 -->
            <div v-if="orderProducts.some(p => !p.count && !p.quantity)" class="mb-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg flex items-start">
              <i class="fas fa-exclamation-triangle text-yellow-600 mt-0.5 mr-2"></i>
              <div class="text-sm text-yellow-800">
                <p class="font-medium">數量資訊提示</p>
                <p class="mt-1">由於系統限制，部分商品的數量資訊可能無法正確顯示。如需確認實際購買數量，請參考訂單總金額或聯繫客戶確認。</p>
              </div>
            </div>
            
            <div class="border rounded-lg overflow-hidden">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="th-sm">商品圖片</th>
                    <th class="th-sm">商品名稱</th>
                    <th class="th-sm">單價</th>
                    <th class="th-sm">
                      數量
                      <span class="text-xs text-gray-500 font-normal ml-1" title="數量資訊可能因後端限制而不準確">
                        <i class="fas fa-info-circle"></i>
                      </span>
                    </th>
                    <th class="th-sm">小計</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="(item, index) in orderProducts" :key="index" class="hover:bg-gray-50">
                    <td class="px-4 py-3">
                      <div class="w-16 h-16 bg-gray-200 rounded overflow-hidden relative">
                        <img
                          :src="getProductImage(item.image)"
                          :alt="item.name"
                          class="w-full h-full object-cover"
                          @error="handleImageError"
                          @load="(e) => console.log('圖片載入成功:', e.target.src)"
                        />
                        <div v-if="!item.image" class="absolute inset-0 flex items-center justify-center text-xs text-gray-500">
                          無圖片
                        </div>
                      </div>
                    </td>
                    <td class="px-4 py-3 text-sm">{{ item.name }}</td>
                    <td class="px-4 py-3 text-sm">NT$ {{ formatNumber(item.price) }}</td>
                    <td class="px-4 py-3 text-sm">
                      <span v-if="item.count || item.quantity">{{ item.count || item.quantity }}</span>
                      <span v-else class="text-gray-400 italic">未知</span>
                    </td>
                    <td class="px-4 py-3 text-sm">
                      NT$ {{ formatNumber(calculateSubtotal(item)) }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 訂單總計 -->
          <div class="flex justify-end border-t pt-4">
            <div class="w-64">
              <div class="flex justify-between mb-2">
                <span>商品總計：</span>
                <span>NT$ {{ formatNumber(selectedOrder.total) }}</span>
              </div>
              <div class="flex justify-between font-semibold text-lg">
                <span>訂單總金額：</span>
                <span>NT$ {{ formatNumber(selectedOrder.total) }}</span>
              </div>
            </div>
          </div>

          <div class="flex justify-end space-x-3 border-t pt-4">
            <button @click="showDetailModal = false" class="btn-gray">關閉</button>
            <button @click="showUpdateStatus(selectedOrder)" class="btn-blue">
              <i class="fas fa-edit mr-2"></i> 更新訂單狀態
            </button>
            <button @click="showUpdatePaymentStatus(selectedOrder)" class="btn-purple">
              <i class="fas fa-money-check-alt mr-2"></i> 更新付款狀態
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 更新付款狀態彈窗 -->
    <div
      v-if="showPaymentStatusModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    >
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold">更新付款狀態</h3>
          <button @click="showPaymentStatusModal = false" class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div v-if="selectedOrder" class="space-y-4">
          <div class="info-group">
            <label class="info-label">訂單編號</label>
            <div class="info-value">{{ selectedOrder.orderId }}</div>
          </div>

          <div class="form-group">
            <label for="paymentStatus" class="form-label">付款狀態</label>
            <select id="paymentStatus" v-model="paymentStatusForm.payment_status" class="form-select">
              <option value="1">已付款</option>
              <option value="0">未付款</option>
            </select>
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <button @click="showPaymentStatusModal = false" class="btn-gray">取消</button>
            <button @click="updatePaymentStatus" class="btn-purple" :disabled="isUpdating">
              <span v-if="isUpdating">處理中...</span>
              <span v-else><i class="fas fa-save mr-2"></i> 確認更新</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 更新訂單狀態彈窗 -->
    <div
      v-if="showStatusModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    >
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold">更新訂單狀態</h3>
          <button @click="showStatusModal = false" class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div v-if="selectedOrder" class="space-y-4">
          <div class="info-group">
            <label class="info-label">訂單編號</label>
            <div class="info-value">{{ selectedOrder.orderId }}</div>
          </div>

          <div class="form-group">
            <label for="orderStatus" class="form-label">訂單狀態</label>
            <select id="orderStatus" v-model="statusForm.status" class="form-select">
              <option value="1">處理中</option>
              <option value="2">已出貨</option>
              <option value="3">已完成</option>
              <option value="4">已取消</option>
            </select>
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <button @click="showStatusModal = false" class="btn-gray">取消</button>
            <button @click="updateOrderStatus" class="btn-blue" :disabled="isUpdating">
              <span v-if="isUpdating">處理中...</span>
              <span v-else><i class="fas fa-save mr-2"></i> 確認更新</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量更新訂單狀態彈窗 -->
    <div
      v-if="showBatchModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    >
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold">批量更新訂單狀態</h3>
          <button @click="showBatchModal = false" class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="space-y-4">
          <p class="text-gray-600">已選擇 {{ selectedOrders.length }} 筆訂單</p>

          <div class="form-group">
            <label for="batchStatus" class="form-label">訂單狀態</label>
            <select id="batchStatus" v-model="batchForm.status" class="form-select">
              <option value="1">處理中</option>
              <option value="2">已出貨</option>
              <option value="3">已完成</option>
              <option value="4">已取消</option>
            </select>
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <button @click="showBatchModal = false" class="btn-gray">取消</button>
            <button @click="updateBatchOrderStatus" class="btn-blue" :disabled="isUpdating">
              <span v-if="isUpdating">處理中...</span>
              <span v-else><i class="fas fa-save mr-2"></i> 確認更新</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量刪除確認彈窗 -->
    <div
      v-if="showDeleteConfirm"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    >
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold text-red-600">確認刪除</h3>
          <button @click="showDeleteConfirm = false" class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="space-y-4">
          <p class="text-gray-700">確定要刪除選中的 {{ selectedOrders.length }} 筆訂單嗎？</p>
          <p class="text-sm text-red-500">此操作無法復原，請謹慎操作！</p>

          <div class="mt-6 flex justify-end space-x-3">
            <button @click="showDeleteConfirm = false" class="btn-gray">取消</button>
            <button @click="deleteBatchOrders" class="btn-red" :disabled="isUpdating">
              <span v-if="isUpdating">處理中...</span>
              <span v-else><i class="fas fa-trash-alt mr-2"></i> 確認刪除</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 設定匯款日期彈窗 -->
    <div
      v-if="showRemittanceDateModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    >
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold">設定匯款日期</h3>
          <button @click="showRemittanceDateModal = false" class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="space-y-4">
          <div class="form-group">
            <label for="remittanceDate" class="form-label">匯款日期</label>
            <input
              type="date"
              id="remittanceDate"
              v-model="remittanceDateForm.remittance_date"
              class="form-input"
            />
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <button @click="showRemittanceDateModal = false" class="btn-gray">取消</button>
            <button @click="updateRemittanceDate" class="btn-blue" :disabled="isUpdating">
              <span v-if="isUpdating">處理中...</span>
              <span v-else><i class="fas fa-save mr-2"></i> 確認設定</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量設定匯款日期彈窗 -->
    <div
      v-if="showBatchRemittanceDateModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    >
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold">批量設定匯款日期</h3>
          <button @click="showBatchRemittanceDateModal = false" class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="space-y-4">
          <p class="text-gray-600">已選擇 {{ selectedOrders.length }} 筆訂單</p>

          <div class="form-group">
            <label for="batchRemittanceDate" class="form-label">匯款日期</label>
            <input
              type="date"
              id="batchRemittanceDate"
              v-model="batchRemittanceDateForm.remittance_date"
              class="form-input"
            />
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <button @click="showBatchRemittanceDateModal = false" class="btn-gray">取消</button>
            <button @click="updateBatchRemittanceDate" class="btn-blue" :disabled="isUpdating">
              <span v-if="isUpdating">處理中...</span>
              <span v-else><i class="fas fa-save mr-2"></i> 確認設定</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import api from '../utils/api.js'
import { getApiUrl } from '../config/apiConfig'
import { getImageUrl as getConfigImageUrl } from '../config/apiConfig'
import dayjs from 'dayjs'
import axios from 'axios'

const { getAdminApiData, postAdminApiData, updateAdminApiData } = api()

const orders = ref([])
const loading = ref(true)
const selectedOrder = ref(null)
const orderProducts = ref([])

// 控制彈窗顯示
const showDetailModal = ref(false)
const showStatusModal = ref(false)
const showBatchModal = ref(false)
const showDeleteConfirm = ref(false)
const showPaymentStatusModal = ref(false)
const showRemittanceDateModal = ref(false)
const showBatchRemittanceDateModal = ref(false)
const isUpdating = ref(false)

// 過濾條件
const filters = reactive({
  orderId: '',
  client: '',
  status: '',
})

// 分頁設置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  totalPages: 1,
  start: 1,
  end: 10,
})

// 狀態更新表單
const statusForm = reactive({
  status: '1',
})

// 付款狀態更新表單
const paymentStatusForm = reactive({
  payment_status: '0',
})

// 批量更新表單
const batchForm = reactive({
  status: '1',
})

// 匯款日期表單
const remittanceDateForm = reactive({
  remittance_date: '',
})

// 批量匯款日期表單
const batchRemittanceDateForm = reactive({
  remittance_date: '',
})

// 多選訂單相關
const selectedOrders = ref([])
const selectAll = computed({
  get: () => {
    return orders.value.length > 0 && selectedOrders.value.length === orders.value.length
  },
  set: (value) => {
    selectedOrders.value = value ? orders.value.map((order) => order.id) : []
  },
})

// 全選/取消全選
const toggleSelectAll = () => {
  selectAll.value = !selectAll.value
}

// 計算顯示的頁碼數
const pageNumbers = computed(() => {
  const totalPages = pagination.totalPages
  const current = pagination.current

  if (totalPages <= 5) {
    return Array.from({ length: totalPages }, (_, i) => i + 1)
  }

  if (current <= 3) {
    return [1, 2, 3, 4, 5]
  }

  if (current >= totalPages - 2) {
    return [totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages]
  }

  return [current - 2, current - 1, current, current + 1, current + 2]
})

// 防抖動搜尋設置
let searchTimeout = null
const debounceSearch = () => {
  // 清除之前的定時器
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  // 設置新的定時器，300毫秒後執行搜尋
  searchTimeout = setTimeout(() => {
    // 重置到第一頁
    pagination.current = 1
    loadOrders()
  }, 300)
}

// 載入訂單列表
const loadOrders = async () => {
  loading.value = true
  try {
    // 構建查詢參數
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
    }

    if (filters.orderId) {
      params.orderId = filters.orderId
    }

    if (filters.client) {
      params.client = filters.client
    }

    if (filters.status) {
      params.status = filters.status
    }

    // 使用更直接的方式訪問API
    const response = await axios.get(getApiUrl('admin/orders'), {
      params: params,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
      }
    })

    console.log('訂單API響應:', response.data)

    if (response.data && response.data.success) {
      orders.value = response.data.orders || []
      pagination.total = response.data.total || 0
      pagination.totalPages = Math.ceil(pagination.total / pagination.pageSize)
      pagination.start = (pagination.current - 1) * pagination.pageSize + 1
      pagination.end = Math.min(pagination.current * pagination.pageSize, pagination.total)
    } else {
      console.error('載入訂單失敗:', response.data?.message)
      // 確保在失敗時也清空訂單列表
      orders.value = []
      pagination.total = 0
      pagination.totalPages = 0
      pagination.start = 0
      pagination.end = 0
    }
  } catch (error) {
    console.error('載入訂單出錯:', error)
    // 確保在錯誤時也清空訂單列表
    orders.value = []
    pagination.total = 0
    pagination.totalPages = 0
    pagination.start = 0
    pagination.end = 0
  } finally {
    loading.value = false
  }
}

// 切換頁碼
const changePage = (page) => {
  if (page < 1 || page > pagination.totalPages) return
  pagination.current = page
  loadOrders()
}

// 獲取付款狀態文字
const getPaymentStatusText = (status) => {
  return status === 1 ? '已付款' : '未付款'
}

// 獲取付款狀態樣式
const getPaymentStatusClass = (status) => {
  return status === 1
    ? 'px-2 py-1 rounded-full text-xs bg-green-100 text-green-800'
    : 'px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800'
}

// 獲取訂單狀態文字
const getStatusText = (status) => {
  const statusMap = {
    '1': '處理中',
    '2': '已出貨',
    '3': '已完成',
    '4': '已取消',
    1: '處理中',
    2: '已出貨',
    3: '已完成',
    4: '已取消',
  }
  return statusMap[status] || '未知'
}

// 獲取訂單狀態類別
const getStatusClass = (status) => {
  const classMap = {
    '1': 'px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800',
    '2': 'px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800',
    '3': 'px-2 py-1 rounded-full text-xs bg-green-100 text-green-800',
    '4': 'px-2 py-1 rounded-full text-xs bg-red-100 text-red-800',
    1: 'px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800',
    2: 'px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800',
    3: 'px-2 py-1 rounded-full text-xs bg-green-100 text-green-800',
    4: 'px-2 py-1 rounded-full text-xs bg-red-100 text-red-800',
  }
  return classMap[status] || ''
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'

  // 使用 dayjs 進行更好的時間格式化
  const date = dayjs(dateString)
  if (!date.isValid()) {
    return dateString
  }

  // 格式化為台灣時間格式：2025年5月24日 下午5:17
  return date.format('YYYY年M月D日 A h:mm')
}

// 格式化匯款日期（只顯示日期部分）
const formatRemittanceDate = (dateString) => {
  if (!dateString) return '-'

  // 如果是純日期格式（YYYY-MM-DD），直接格式化
  if (dateString.length === 10 && dateString.includes('-')) {
    const date = dayjs(dateString)
    if (date.isValid()) {
      return date.format('YYYY年M月D日')
    }
  }

  // 如果是完整的日期時間格式，提取日期部分
  const date = dayjs(dateString)
  if (!date.isValid()) {
    return dateString
  }

  return date.format('YYYY年M月D日')
}

// 格式化數字
const formatNumber = (num) => {
  if (num === undefined || num === null) return '0'
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 顯示訂單詳情
const showOrderDetail = async (order) => {
  selectedOrder.value = { ...order };

  try {
    // 使用直接的 axios 請求獲取訂單詳情
    const response = await axios.get(getApiUrl(`admin/orders/${order.id}`), {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
      }
    });

    console.log('訂單詳情API響應:', response.data);

    if (response.data.success && response.data.orderDetails) {
      // 處理商品數據，確保數量欄位存在
      const products = response.data.orderDetails.products || [];
      
      // 確保每個商品都有數量欄位
      orderProducts.value = products.map((product, index) => {
        // 如果商品數據是字符串（可能是 JSON），先解析
        if (typeof product === 'string') {
          try {
            product = JSON.parse(product);
          } catch (e) {
            console.error('解析商品數據失敗:', e);
            return product;
          }
        }
        
        // 嘗試從商品數據中獲取數量
        let productCount = product.count || product.quantity || 0;
        
        // 如果沒有數量資訊，標記為未知
        if (!productCount) {
          console.warn(`商品 "${product.name}" 缺少數量資訊`);
          productCount = null; // 使用 null 表示數量未知
        }
        
        return {
          ...product,
          count: productCount,
          quantity: productCount
        };
      });
      
      console.log('處理後的訂單商品列表:', orderProducts.value);
      
      // 更新選中的訂單詳情
      selectedOrder.value = {
        ...selectedOrder.value,
        ...response.data.orderDetails
      };
    } else {
      console.error('訂單詳情獲取失敗:', response.data.message);
    }

  } catch (error) {
    console.error('獲取訂單詳情錯誤:', error);
  }

  showDetailModal.value = true;
};

// 顯示更新訂單狀態彈窗
const showUpdateStatus = (order) => {
  selectedOrder.value = { ...order }
  statusForm.status = order.status.toString()
  showStatusModal.value = true
}

// 更新訂單狀態
const updateOrderStatus = async () => {
  if (!selectedOrder.value) return

  isUpdating.value = true

  try {
    const response = await updateAdminApiData('admin/orders/status', {
      id: selectedOrder.value.id,
      status: parseInt(statusForm.status),
    })

    if (response.success) {
      // 更新本地訂單狀態
      const index = orders.value.findIndex((o) => o.id === selectedOrder.value.id)
      if (index !== -1) {
        orders.value[index].status = parseInt(statusForm.status)
      }

      // 更新選中的訂單狀態
      if (selectedOrder.value) {
        selectedOrder.value.status = parseInt(statusForm.status)
      }

      // 關閉彈窗
      showStatusModal.value = false

      alert('訂單狀態已成功更新')
    } else {
      alert(`更新失敗: ${response.message}`)
    }
  } catch (error) {
    console.error('更新訂單狀態出錯:', error)
    alert('更新訂單狀態時發生錯誤，請稍後再試')
  } finally {
    isUpdating.value = false
  }
}

// 顯示批量更新狀態彈窗
const showBatchStatusModal = () => {
  if (selectedOrders.value.length === 0) {
    alert('請至少選擇一筆訂單')
    return
  }
  showBatchModal.value = true
}

// 顯示批量刪除確認彈窗
const showBatchDeleteConfirm = () => {
  if (selectedOrders.value.length === 0) {
    alert('請至少選擇一筆訂單')
    return
  }
  showDeleteConfirm.value = true
}

// 批量更新訂單狀態
const updateBatchOrderStatus = async () => {
  if (selectedOrders.value.length === 0) return

  isUpdating.value = true

  try {
    const response = await updateAdminApiData('admin/orders/batch-status', {
      ids: selectedOrders.value,
      status: parseInt(batchForm.status),
    })

    if (response.success) {
      // 更新本地訂單狀態
      for (const id of selectedOrders.value) {
        const index = orders.value.findIndex((o) => o.id === id)
        if (index !== -1) {
          orders.value[index].status = parseInt(batchForm.status)
        }
      }

      // 關閉彈窗
      showBatchModal.value = false
      selectedOrders.value = []

      alert('訂單狀態已成功更新')
    } else {
      alert(`更新失敗: ${response.message}`)
    }
  } catch (error) {
    console.error('批量更新訂單狀態出錯:', error)
    alert('更新訂單狀態時發生錯誤，請稍後再試')
  } finally {
    isUpdating.value = false
  }
}

// 批量刪除訂單
const deleteBatchOrders = async () => {
  if (selectedOrders.value.length === 0) return

  isUpdating.value = true

  try {
    const response = await postAdminApiData('admin/orders/batch-delete', {
      ids: selectedOrders.value,
    })

    if (response.success) {
      // 清空選擇和關閉彈窗
      selectedOrders.value = []
      showDeleteConfirm.value = false

      // 重新載入訂單列表以確保數據同步
      await loadOrders()

      alert(response.message || '所選訂單已成功刪除')
    } else {
      alert(`刪除失敗: ${response.message}`)
    }
  } catch (error) {
    console.error('批量刪除訂單出錯:', error)
    alert('刪除訂單時發生錯誤，請稍後再試')
  } finally {
    isUpdating.value = false
  }
}

// 顯示更新付款狀態彈窗
const showUpdatePaymentStatus = (order) => {
  selectedOrder.value = { ...order }
  paymentStatusForm.payment_status = order.payment_status.toString()
  showPaymentStatusModal.value = true
}

// 顯示設定匯款日期彈窗
const showUpdateRemittanceDate = (order) => {
  selectedOrder.value = { ...order }
  // 如果已有匯款日期，轉換為日期格式
  if (order.remittance_date) {
    remittanceDateForm.remittance_date = dayjs(order.remittance_date).format('YYYY-MM-DD')
  } else {
    remittanceDateForm.remittance_date = ''
  }
  showRemittanceDateModal.value = true
}

// 顯示批量設定匯款日期彈窗
const showBatchRemittanceDateModalFunc = () => {
  if (selectedOrders.value.length === 0) {
    alert('請至少選擇一筆訂單')
    return
  }
  batchRemittanceDateForm.remittance_date = ''
  showBatchRemittanceDateModal.value = true
}

// 更新付款狀態
const updatePaymentStatus = async () => {
  if (!selectedOrder.value) return

  isUpdating.value = true

  try {
    const response = await updateAdminApiData('admin/orders/payment-status', {
      id: selectedOrder.value.id,
      payment_status: parseInt(paymentStatusForm.payment_status),
    })

    if (response.success) {
      // 更新本地訂單狀態
      const index = orders.value.findIndex((o) => o.id === selectedOrder.value.id)
      if (index !== -1) {
        orders.value[index].payment_status = parseInt(paymentStatusForm.payment_status)
        // 如果成功，也可能需要更新付款日期
        orders.value[index].payment_date = new Date().toISOString();
      }

      // 更新選中的訂單狀態
      if (selectedOrder.value) {
        selectedOrder.value.payment_status = parseInt(paymentStatusForm.payment_status)
        selectedOrder.value.payment_date = new Date().toISOString();
      }

      // 關閉彈窗
      showPaymentStatusModal.value = false

      alert('付款狀態已成功更新')
    } else {
      alert(`更新失敗: ${response.message}`)
    }
  } catch (error) {
    console.error('更新付款狀態出錯:', error)
    alert('更新付款狀態時發生錯誤，請稍後再試')
  } finally {
    isUpdating.value = false
  }
}

// 更新匯款日期
const updateRemittanceDate = async () => {
  if (!selectedOrder.value) return

  isUpdating.value = true

  try {
    const response = await updateAdminApiData('admin/orders/remittance-date', {
      id: selectedOrder.value.id,
      remittance_date: remittanceDateForm.remittance_date || null,
    })

    if (response.success) {
      // 更新本地訂單的匯款日期
      const index = orders.value.findIndex((o) => o.id === selectedOrder.value.id)
      if (index !== -1) {
        orders.value[index].remittance_date = remittanceDateForm.remittance_date || null
      }

      // 更新選中的訂單
      if (selectedOrder.value) {
        selectedOrder.value.remittance_date = remittanceDateForm.remittance_date || null
      }

      // 關閉彈窗
      showRemittanceDateModal.value = false

      alert('匯款日期已成功更新')
    } else {
      alert(`更新失敗: ${response.message}`)
    }
  } catch (error) {
    console.error('更新匯款日期出錯:', error)
    alert('更新匯款日期時發生錯誤，請稍後再試')
  } finally {
    isUpdating.value = false
  }
}

// 批量更新匯款日期
const updateBatchRemittanceDate = async () => {
  if (selectedOrders.value.length === 0) return

  isUpdating.value = true

  try {
    const response = await updateAdminApiData('admin/orders/batch-remittance-date', {
      ids: selectedOrders.value,
      remittance_date: batchRemittanceDateForm.remittance_date || null,
    })

    if (response.success) {
      // 更新本地訂單的匯款日期
      selectedOrders.value.forEach(orderId => {
        const index = orders.value.findIndex((o) => o.id === orderId)
        if (index !== -1) {
          orders.value[index].remittance_date = batchRemittanceDateForm.remittance_date || null
        }
      })

      // 清空選擇和關閉彈窗
      selectedOrders.value = []
      showBatchRemittanceDateModal.value = false

      alert('匯款日期已成功批量更新')
    } else {
      alert(`更新失敗: ${response.message}`)
    }
  } catch (error) {
    console.error('批量更新匯款日期出錯:', error)
    alert('批量更新匯款日期時發生錯誤，請稍後再試')
  } finally {
    isUpdating.value = false
  }
}

const getDefaultImagePath = () => {
  return import.meta.env.DEV ? '/no-image.jpg' : 'http://60.198.79.27:81/no-image.jpg'
}

// 處理圖片 URL
const getImagePath = (path) => {
  const defaultImage = getDefaultImagePath()
  
  if (!path) {
    console.log('無圖片路徑，使用默認圖片')
    return defaultImage
  }

  // 如果已經是完整URL，直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    console.log('完整URL圖片:', path)
    return path
  }
  
  // 如果路徑以 /uploads/ 開頭，使用API伺服器URL
  if (path.startsWith('/uploads/')) {
    const fullUrl = `${getApiUrl()}${path}`
    console.log('完整圖片URL:', fullUrl)
    return fullUrl
  }
  
  // 如果路徑不包含 /uploads/ 且不是絕對路徑，添加前綴
  if (!path.startsWith('/')) {
    const fullUrl = `${getApiUrl()}/uploads/products/${path}`
    console.log('添加前綴後的圖片URL:', fullUrl)
    return fullUrl
  }
  
  // 其他情況直接添加伺服器前綴
  const fullUrl = `${getApiUrl()}${path}`
  console.log('最終圖片URL:', fullUrl)
  return fullUrl
}

// 處理圖片加載錯誤
const handleImageError = (e) => {
  console.log('圖片加載失敗:', e.target.src)

  // 隱藏圖片元素，讓灰色背景顯示
  e.target.style.display = 'none'

  // 在父 div 中添加錯誤提示
  const parentDiv = e.target.parentNode
  if (parentDiv && !parentDiv.querySelector('.error-text')) {
    const errorSpan = document.createElement('span')
    errorSpan.textContent = '無圖片'
    errorSpan.className =
      'text-xs text-gray-500 error-text absolute inset-0 flex items-center justify-center'
    parentDiv.appendChild(errorSpan)
    
    // 輸出更多調試信息
    console.log('圖片元素:', e.target)
    console.log('圖片父元素:', parentDiv)
    console.log('圖片URL:', e.target.src)
    console.log('圖片替代文字:', e.target.alt)
    
    // 嘗試使用本地靜態圖片作為備用
    const staticImage = new Image()
    staticImage.onload = () => {
      e.target.src = staticImage.src
      e.target.style.display = 'block'
      if (parentDiv.querySelector('.error-text')) {
        parentDiv.querySelector('.error-text').remove()
      }
    }
    staticImage.onerror = () => {
      console.log('靜態備用圖片也加載失敗')
    }
    staticImage.src = getDefaultImagePath()
  }
}

// 計算商品小計
const calculateSubtotal = (item) => {
  const price = parseFloat(item.price) || 0
  // 優先使用 count，如果沒有則使用 quantity
  const quantity = parseInt(item.count || item.quantity) || 0
  
  // 如果數量為 0 或未知，返回價格（假設至少買了一個）
  if (quantity === 0) {
    return price
  }
  
  return price * quantity
}

// 獲取商品圖片
const getProductImage = (imagePath) => {
  try {
    // 如果是商品圖片路徑字符串（可能是 JSON 字符串）
    if (typeof imagePath === 'string') {
      // 嘗試解析 JSON
      try {
        const images = JSON.parse(imagePath)
        if (Array.isArray(images) && images.length > 0) {
          return getConfigImageUrl(images[0], 'product')
        }
      } catch (e) {
        // 如果不是 JSON，直接使用原始路徑
        return getConfigImageUrl(imagePath, 'product')
      }
    }
    
    // 如果沒有有效圖片，返回默認圖片
    return getDefaultImagePath()
  } catch (e) {
    console.error('解析商品圖片時出錯:', e)
    return getDefaultImagePath()
  }
}

// 當組件掛載時載入數據
onMounted(() => {
  loadOrders()
})
</script>

<style scoped>
.btn-blue {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded;
}

.btn-gray {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2 px-4 rounded;
}

.btn-gray-sm {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-1 px-2 rounded;
}

.btn-blue-sm {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-semibold py-1 px-3 rounded;
}

.btn-red-sm {
  @apply bg-red-600 hover:bg-red-700 text-white font-semibold py-1 px-3 rounded;
}

.btn-red {
  @apply bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded;
}

.btn-green-sm {
  @apply bg-green-600 hover:bg-green-700 text-white font-semibold py-1 px-3 rounded;
}

.form-input,
.form-select,
.form-textarea {
  @apply w-full rounded-md border-2 border-gray-400 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-300 focus:ring-opacity-50 bg-white px-3 py-2;
}

.th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.th-sm {
  @apply px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.page-number {
  @apply cursor-pointer px-3 py-1 rounded text-sm;
}

.spinner {
  @apply mx-auto h-8 w-8 border-4 border-blue-200 rounded-full border-t-blue-600 animate-spin;
}

.info-group {
  @apply mb-2;
}

.info-label {
  @apply block text-sm font-medium text-gray-500;
}

.info-value {
  @apply mt-1 text-sm text-gray-900;
}

.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.th-checkbox {
  @apply px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-12;
}

.form-checkbox {
  @apply rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50;
}

.btn-red-sm:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.btn-purple {
  @apply bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2 px-4 rounded inline-flex items-center transition-colors;
}

.btn-purple:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.action-btn {
  @apply w-10 h-10 flex items-center justify-center rounded-full transition-colors duration-150 ease-in-out;
}

.action-btn.view {
  @apply text-blue-600 hover:bg-blue-100;
}

.action-btn.edit {
  @apply text-green-600 hover:bg-green-100;
}

.action-btn.payment {
  @apply text-purple-600 hover:bg-purple-100;
}
</style>
