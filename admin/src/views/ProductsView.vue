<template>
  <div class="products-admin">
    <div class="mb-6 flex justify-end items-center">
      <RouterLink to="products/add" class="btn-primary">
        <i class="fas fa-plus mr-2"></i> 新增商品
      </RouterLink>
    </div>

    <!-- 搜尋與過濾區域 -->
    <div class="bg-white p-4 rounded-lg shadow mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">商品名稱</label>
          <div class="relative">
            <input
              ref="searchInputRef"
              type="text"
              v-model="filters.name"
              placeholder="搜尋商品名稱"
              class="filter-input w-full"
              :class="{ 'pr-16': filters.name || searchLoading }"
              @input="handleSearch"
            />
            <!-- 搜尋載入指示器 -->
            <div v-show="searchLoading" class="absolute right-8 top-1/2 -translate-y-1/2">
              <div class="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            </div>
            <!-- 清除搜尋按鈕 -->
            <button
              v-if="filters.name && !searchLoading"
              @click="clearSearch"
              class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
        
        <!-- 🔧 使用新的分類篩選組件 -->
        <div>
          <CategoryFilter
            v-model="filters.categoryId"
            @categoryChange="handleCategoryFilterChange"
          />
        </div>

        <!-- 新增：商品狀態篩選器 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">商品狀態</label>
          <select
            v-model="filters.status"
            @change="handleStatusFilterChange"
            class="filter-select w-full"
          >
            <option value="active">僅顯示啟用商品</option>
            <option value="inactive">僅顯示停用商品</option>
            <option value="all">顯示全部商品</option>
          </select>
        </div>

        <!-- 篩選器重置按鈕 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">&nbsp;</label>
          <button
            @click="resetFilters"
            class="filter-button w-full"
            title="重置所有篩選條件"
          >
            <i class="fas fa-undo mr-2"></i>
            重置篩選
          </button>
        </div>
      </div>
    </div>

    <!-- 商品列表 -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <!-- 狀態統計條 -->
      <div v-if="!loading || products.length > 0" class="px-6 py-3 bg-gray-50 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4 text-sm text-gray-600">
            <div class="flex items-center">
              <span class="font-medium">篩選結果:</span>
              <span class="ml-2">{{ pagination.total }} 個商品</span>
            </div>
            <div v-if="filters.status === 'active'" class="flex items-center text-green-600">
              <i class="fas fa-check-circle mr-1"></i>
              <span>僅顯示啟用商品</span>
            </div>
            <div v-else-if="filters.status === 'inactive'" class="flex items-center text-red-600">
              <i class="fas fa-ban mr-1"></i>
              <span>僅顯示停用商品</span>
            </div>
            <div v-else class="flex items-center text-blue-600">
              <i class="fas fa-list mr-1"></i>
              <span>顯示全部商品</span>
            </div>
            <div v-if="filters.name" class="flex items-center text-purple-600">
              <i class="fas fa-search mr-1"></i>
              <span>搜尋: "{{ filters.name }}"</span>
            </div>
            <div v-if="filters.categoryId" class="flex items-center text-orange-600">
              <i class="fas fa-tags mr-1"></i>
              <span>分類篩選中</span>
            </div>
          </div>
          <div v-if="hasActiveFilters" class="text-sm">
            <button @click="resetFilters" class="text-blue-600 hover:text-blue-800 underline">
              <i class="fas fa-undo mr-1"></i>
              清除篩選
            </button>
          </div>
        </div>
      </div>

      <!-- 載入狀態 -->
      <div v-show="loading && products.length === 0" class="p-6 text-center">
        <div class="flex items-center justify-center mb-4">
          <div class="spinner mr-3"></div>
          <span class="text-gray-600">載入商品資料中...</span>
        </div>
        
        <!-- 商品列表骨架屏 -->
        <div class="space-y-4">
          <div v-for="i in 5" :key="i" class="animate-pulse">
            <div class="flex items-center space-x-4 p-4 border-b">
              <div class="w-16 h-16 bg-gray-200 rounded"></div>
              <div class="flex-1 space-y-2">
                <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                <div class="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div class="w-24 h-4 bg-gray-200 rounded"></div>
              <div class="w-20 h-4 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 無資料狀態 -->
      <div v-else-if="!loading && products.length === 0" class="p-6 text-center text-gray-500">
        <i class="fas fa-box-open text-5xl mb-4"></i>
        <p>暫無商品資料</p>
      </div>
      
      <!-- 商品表格 -->
      <div v-else class="relative">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="th">商品圖片</th>
                <th class="th">商品名稱</th>
                <th class="th">分類</th>
                <th class="th">價格</th>
                <th class="th">特價</th>
                <th class="th">特色商品</th>
                <th class="th">商品狀態</th>
                <th class="th">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" :class="{ 'opacity-60': loading }">
              <tr v-for="product in products" :key="product.id" 
                  class="hover:bg-gray-50 transition-colors" 
                  :class="{ 
                    'opacity-50': !product.is_active && filters.status === 'all',
                    'bg-red-50': !product.is_active && filters.status === 'inactive'
                  }">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div
                    class="w-16 h-16 bg-gray-200 rounded overflow-hidden relative flex items-center justify-center"
                  >
                    <img
                      v-if="getImageUrl(product)"
                      :src="getImageUrl(product)"
                      alt="商品圖片"
                      class="w-full h-full object-cover"
                      @error="onImageError"
                      loading="lazy"
                    />
                    <span v-else class="text-xs text-gray-500">無圖片</span>
                    <!-- 停用商品的覆蓋層 -->
                    <div v-if="!product.is_active" class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                      <i class="fas fa-ban text-red-500 text-xl"></i>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" 
                    :class="product.is_active ? 'text-gray-900' : 'text-gray-500'">
                  {{ product.name }}
                  <div v-if="!product.is_active" class="flex items-center mt-1">
                    <span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">已停用</span>
                  </div>
                  <div v-else-if="product.is_featured" class="flex items-center mt-1">
                    <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">特色商品</span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ product.category_path || product.sort || '未分類' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  NT$ {{ formatNumber(product.price1) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span v-if="product.price2" class="text-green-600"
                    >NT$ {{ formatNumber(product.price2) }}</span
                  >
                  <span v-else>-</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div class="relative inline-block w-10 mr-2 align-middle select-none">
                    <input
                      type="checkbox"
                      :id="'toggle-featured-' + product.id"
                      :checked="product.is_featured"
                      @change="toggleFeatured(product)"
                      class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                    />
                    <label
                      :for="'toggle-featured-' + product.id"
                      class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"
                    ></label>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div class="relative inline-block w-10 mr-2 align-middle select-none">
                    <input
                      type="checkbox"
                      :id="'toggle-active-' + product.id"
                      :checked="product.is_active"
                      @change="toggleActive(product)"
                      class="toggle-checkbox-active absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                    />
                    <label
                      :for="'toggle-active-' + product.id"
                      class="toggle-label-active block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"
                    ></label>
                  </div>
                  <span class="text-xs ml-2" :class="product.is_active ? 'text-green-600' : 'text-red-600'">
                    {{ product.is_active ? '啟用' : '停用' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div class="flex space-x-2">
                    <RouterLink
                      :to="{
                        path: `products/edit/${product.id}`,
                        query: {
                          keyword: filters.name || undefined,
                          categoryId: filters.categoryId || undefined,
                          page: pagination.currentPage > 1 ? pagination.currentPage : undefined,
                          pageSize: pagination.pageSize !== 20 ? pagination.pageSize : undefined
                        }
                      }"
                      class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-100"
                      title="編輯"
                    >
                      <i class="fas fa-edit"></i>
                    </RouterLink>
                    <button
                      @click="confirmDelete(product)"
                      class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-100"
                      title="刪除"
                    >
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- 載入中遮罩 -->
        <div v-show="loading" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div class="flex items-center">
            <div class="spinner mr-3"></div>
            <span class="text-gray-600">更新資料中...</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 分頁控制器 -->
    <div v-if="!loading || products.length > 0" class="mt-6">
      <AdminPageItem
        :total="pagination.total"
        :currentPage="pagination.currentPage"
        :pageSize="pagination.pageSize"
        :loading="loading"
        @pageChange="handlePageChange"
        @pageSizeChange="handlePageSizeChange"
      />
    </div>

    <!-- 刪除確認彈窗 -->
    <div
      v-if="showDeleteModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    >
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
        <h3 class="text-xl font-semibold mb-4">確認刪除</h3>
        <p>您確定要刪除商品「{{ selectedProduct?.name }}」嗎？此操作無法還原。</p>
        <div class="mt-6 flex justify-end space-x-3">
          <button @click="showDeleteModal = false" class="btn-gray">取消</button>
          <button @click="deleteProduct" class="btn-red">
            <i class="fas fa-trash mr-2"></i> 確認刪除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { RouterLink } from 'vue-router'
import api from '../utils/api.js'
import _ from 'lodash'
import { useRouter } from 'vue-router'
import CategoryFilter from '../components/CategoryFilter.vue'
import AdminPageItem from '../components/AdminPageItem.vue'

const { getAdminApiData, deleteAdminApiData, postAdminApiData } = api()
const { getApiData } = api() // 額外導入前台API用於商品查詢
const router = useRouter()

const products = ref([])
const loading = ref(true)
const searchLoading = ref(false) // 專門用於搜尋的載入狀態
const showDeleteModal = ref(false)
const selectedProduct = ref(null)
const searchInputRef = ref(null)

// 計算屬性：檢查是否有啟用的篩選器
const hasActiveFilters = computed(() => {
  return filters.name !== '' || 
         filters.categoryId !== '' || 
         filters.status !== 'active' // 預設是 active，所以只有非 active 才算是篩選
})

// 分頁狀態
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0,
})

// 過濾條件
const filters = reactive({
  name: '',
  categoryId: '',
  status: 'active', // 預設只顯示啟用商品
})

// 載入商品列表 - 支援分頁 - 優化版本
const loadProducts = async (preserveFocus = false, isSearch = false) => {
  // 保存當前焦點元素
  const activeElement = preserveFocus ? document.activeElement : null

  // 如果是搜尋，使用 searchLoading，否則使用 loading
  if (isSearch) {
    searchLoading.value = true
  } else {
    loading.value = true
  }

  try {
    // 準備後台API參數
    const params = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
    }

    // 添加搜索關鍵字
    if (filters.name && filters.name.trim() !== '') {
      params.keyWord = filters.name.trim()
    }

    // 處理分類篩選，確保數據類型正確
    if (filters.categoryId !== null && filters.categoryId !== undefined && filters.categoryId !== '') {
      // 將 categoryId 轉換為字符串再處理
      const categoryIdStr = String(filters.categoryId).trim()
      if (categoryIdStr !== '') {
        params.categoryId = categoryIdStr
      }
    }

    // 添加狀態篩選
    if (filters.status && filters.status !== '') {
      params.status = filters.status
    }

    // 使用後台API
    const apiResponse = await getAdminApiData('admin/products', params)

    // 檢查後台API返回的數據格式
    if (apiResponse && apiResponse.success) {
      const productsData = apiResponse.products || []
      const total = apiResponse.total || 0

      // 設置商品數據和分頁資訊
      products.value = productsData
      pagination.total = total
    } else {
      products.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('載入商品異常:', error)
    products.value = []
    pagination.total = 0
  } finally {
    // 根據載入類型設置對應的狀態
    if (isSearch) {
      searchLoading.value = false
    } else {
      loading.value = false
    }

    // 恢復焦點到搜尋輸入框
    if (preserveFocus && activeElement && activeElement === searchInputRef.value) {
      // 使用 setTimeout 確保 DOM 更新完成後再恢復焦點
      setTimeout(() => {
        if (searchInputRef.value) {
          searchInputRef.value.focus()
          // 將光標移到文字末尾
          const length = searchInputRef.value.value.length
          searchInputRef.value.setSelectionRange(length, length)
        }
      }, 0)
    }
  }
}

// 處理分頁變更 - 優化版本
const handlePageChange = (page) => {
  pagination.currentPage = page
  
  // 更新URL參數
  updateUrlParams({ page: page > 1 ? page : null })
  
  loadProducts()
  
  // 滾動到頂部
  scrollToTop()
}

// 處理每頁數量變更 - 優化版本
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.currentPage = 1 // 重置到第一頁
  
  // 更新URL參數
  updateUrlParams({ 
    pageSize: pageSize !== 20 ? pageSize : null, // 預設是20，不用記錄
    page: null // 重置頁碼
  })
  
  loadProducts()
  
  // 滾動到頂部
  scrollToTop()
}

// 滾動到頂部的函數
const scrollToTop = () => {
  // 多種滾動方式，確保在不同瀏覽器中都能正常工作
  try {
    // 方式1: 使用 window.scrollTo
    window.scrollTo({ top: 0, behavior: 'smooth' })
    
    // 方式2: 備用方案
    setTimeout(() => {
      document.documentElement.scrollTop = 0
      document.body.scrollTop = 0
    }, 100)
  } catch (error) {
    console.log('滾動到頂部失敗:', error)
  }
}

// 當分類過濾器變更時 - 優化版本
const handleCategoryFilterChange = (categoryParams) => {
  // 確保 categoryId 數據類型處理正確
  const categoryId = categoryParams.categoryId
  filters.categoryId = (categoryId !== null && categoryId !== undefined) ? String(categoryId) : ''
  pagination.currentPage = 1 // 重置到第一頁
  
  // 更新URL參數
  updateUrlParams({ 
    categoryId: filters.categoryId || null,
    page: null // 重置頁碼
  })
  
  loadProducts()
}

// 處理搜索 - 完全避免重新渲染的版本
const handleSearch = _.debounce(async () => {
  // 保存當前焦點
  const activeElement = document.activeElement
  const isSearchInput = activeElement === searchInputRef.value

  pagination.currentPage = 1 // 重置到第一頁

  // 更新URL參數，包含搜索關鍵字
  updateUrlParams({
    keyWord: filters.name || null,
    page: null // 重置頁碼
  })

  // 設置搜尋載入狀態，但不觸發主要載入狀態
  searchLoading.value = true

  try {
    // 準備後台API參數
    const params = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
    }

    // 添加搜索關鍵字
    if (filters.name && filters.name.trim() !== '') {
      params.keyWord = filters.name.trim()
    }

    // 處理分類篩選
    if (filters.categoryId !== null && filters.categoryId !== undefined && filters.categoryId !== '') {
      const categoryIdStr = String(filters.categoryId).trim()
      if (categoryIdStr !== '') {
        params.categoryId = categoryIdStr
      }
    }

    // 添加狀態篩選
    if (filters.status && filters.status !== '') {
      params.status = filters.status
    }

    // 使用後台API
    const apiResponse = await getAdminApiData('admin/products', params)

    // 檢查後台API返回的數據格式
    if (apiResponse && apiResponse.success) {
      const productsData = apiResponse.products || []
      const total = apiResponse.total || 0

      // 設置商品數據和分頁資訊
      products.value = productsData
      pagination.total = total
    } else {
      products.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('搜尋商品異常:', error)
    products.value = []
    pagination.total = 0
  } finally {
    searchLoading.value = false

    // 恢復焦點
    if (isSearchInput && searchInputRef.value) {
      setTimeout(() => {
        searchInputRef.value.focus()
        const length = searchInputRef.value.value.length
        searchInputRef.value.setSelectionRange(length, length)
      }, 0)
    }
  }
}, 300)

// 清除搜尋
const clearSearch = () => {
  filters.name = ''
  pagination.currentPage = 1 // 重置到第一頁
  
  // 更新URL參數，移除搜索關鍵字
  updateUrlParams({ 
    keyWord: null,
    page: null // 重置頁碼
  })
  
  loadProducts()
}

// 更新URL參數
const updateUrlParams = (params) => {
  // 獲取當前路由參數
  const currentQuery = { ...router.currentRoute.value.query }
  
  // 更新參數
  Object.entries(params).forEach(([key, value]) => {
    if (value === null || value === undefined || value === '') {
      // 如果值為空，則移除該參數
      delete currentQuery[key]
    } else {
      // 否則更新或添加參數
      currentQuery[key] = value
    }
  })
  
  // 使用replace方法更新URL，不添加新的歷史記錄
  router.replace({ query: currentQuery })
}

// 從URL參數中恢復狀態 - 優化版本
const restoreStateFromUrl = () => {
  const query = router.currentRoute.value.query
  
  // 恢復搜索關鍵字
  if (query.keyWord) {
    filters.name = query.keyWord
  }
  
  // 恢復分類過濾，確保數據類型正確
  if (query.categoryId) {
    filters.categoryId = String(query.categoryId)
  }

  // 恢復狀態過濾
  if (query.status) {
    filters.status = query.status
  }
  
  // 恢復分頁狀態
  if (query.page) {
    pagination.currentPage = parseInt(query.page) || 1
  }
  
  if (query.pageSize) {
    pagination.pageSize = parseInt(query.pageSize) || 20
  }
}

// 確認刪除商品
const confirmDelete = (product) => {
  selectedProduct.value = product
  showDeleteModal.value = true
}

// 刪除商品
const deleteProduct = async () => {
  try {
    // 直接使用 admin/products 端點，並傳遞 id 作為第二個參數
    const response = await deleteAdminApiData('admin/products', selectedProduct.value.id)

    if (response && response.success) {
      // 重新載入商品列表
      loadProducts()
      showDeleteModal.value = false
      alert('商品已成功刪除')
    } else {
      alert(`刪除失敗: ${response ? response.message : '未知錯誤'}`)
    }
  } catch (error) {
    console.error('刪除商品出錯:', error)
    alert('刪除商品時發生錯誤，請稍後再試')
  }
}

// 從 apiConfig 導入圖片路徑函數
import { getImageUrl as getConfigImageUrl } from '../config/apiConfig'

// 🔧 優化：添加圖片URL快取
const imageUrlCache = new Map()

// 獲取圖片 URL 或 null - 優化版本
const getImageUrl = (product) => {
  if (!product || !product.images) return null

  // 檢查快取
  const cacheKey = `${product.id}-${product.images}`
  if (imageUrlCache.has(cacheKey)) {
    return imageUrlCache.get(cacheKey)
  }

  let images = null
  
  // 檢查 images 是否已經是數組
  if (Array.isArray(product.images)) {
    images = product.images
  } else if (typeof product.images === 'string') {
    // 如果是字符串，嘗試解析為JSON
    try {
      images = JSON.parse(product.images)
    } catch (error) {
      // 如果解析失敗，但看起來像一個URL，直接使用
      const imageStr = product.images.trim()
      if (imageStr.startsWith('http://') || imageStr.startsWith('https://')) {
        const result = imageStr
        imageUrlCache.set(cacheKey, result)
        return result
      }
      // 如果是本地路徑
      if (imageStr.startsWith('/uploads/')) {
        const result = getConfigImageUrl(imageStr, 'product')
        imageUrlCache.set(cacheKey, result)
        return result
      }
      imageUrlCache.set(cacheKey, null)
      return null
    }
  }

  // 處理數組格式的圖片數據
  if (images && Array.isArray(images) && images.length > 0) {
    let image = images[0]
    
    // 如果是完整的 HTTP URL，直接返回
    if (image.startsWith('http://') || image.startsWith('https://')) {
      imageUrlCache.set(cacheKey, image)
      return image
    }
    
    // 如果是本地路徑，使用 apiConfig 處理
    const processedUrl = getConfigImageUrl(image, 'product')
    imageUrlCache.set(cacheKey, processedUrl)
    return processedUrl
  }

  imageUrlCache.set(cacheKey, null)
  return null // 返回 null 表示沒有有效的圖片
}

// 處理圖片載入錯誤 - 優化版本
const onImageError = (event) => {
  // 減少錯誤處理的複雜度
  const originalSrc = event.target.src
  
  // 隱藏圖片元素，讓灰色背景顯示
  event.target.style.display = 'none'
  
  // 在父 div 中添加錯誤提示（避免重複添加）
  const parentDiv = event.target.parentNode
  if (parentDiv && !parentDiv.querySelector('.error-text')) {
    const errorSpan = document.createElement('span')
    errorSpan.textContent = originalSrc.includes('placeholder.com') ? '圖片無法載入' : '載入錯誤'
    errorSpan.className = 'text-xs text-red-500 error-text'
    parentDiv.appendChild(errorSpan)
  }
}

// 格式化數字
const formatNumber = (num) => {
  if (num === undefined || num === null) return '0'
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 在 script setup 部分添加 toggleFeatured 函數
const toggleFeatured = async (product) => {
  try {
    const response = await postAdminApiData('admin/products/toggle-featured', {
      id: product.id,
      is_featured: !product.is_featured,
    })

    if (response && response.success) {
      product.is_featured = !product.is_featured
    } else {
      alert('更新特色商品狀態失敗：' + (response?.message || '未知錯誤'))
    }
  } catch (error) {
    console.error('更新特色商品狀態時發生錯誤:', error)
    alert('更新特色商品狀態時發生錯誤，請稍後再試')
  }
}

// 新增功能：切換商品啟用/停用狀態
const toggleActive = async (product) => {
  try {
    const response = await postAdminApiData('admin/products/toggle-active', {
      id: product.id,
      is_active: !product.is_active,
    })

    if (response && response.success) {
      product.is_active = !product.is_active
      // 顯示成功訊息
      const statusText = product.is_active ? '啟用' : '停用'
      alert(`商品已${statusText}`)
    } else {
      alert('更新商品狀態失敗：' + (response?.message || '未知錯誤'))
    }
  } catch (error) {
    console.error('更新商品狀態時發生錯誤:', error)
    alert('更新商品狀態時發生錯誤，請稍後再試')
  }
}

// 獲取返回路徑，保留當前頁碼 - 優化版本
const getReturnPath = () => {
  // 檢查是否有來源頁碼
  const page = route.query.from_page || route.query.page
  const keyword = route.query.keyword
  const categoryId = route.query.categoryId
  
  // 構建查詢參數對象
  const query = {}
  
  // 添加頁碼參數
  if (page) {
    query.page = page
  }
  
  // 添加關鍵字搜索參數
  if (keyword) {
    query.keyWord = keyword
  }
  
  // 添加分類過濾參數
  if (categoryId) {
    query.categoryId = categoryId
  }
  
  // 如果有任何查詢參數，返回帶參數的路徑
  if (Object.keys(query).length > 0) {
    return { path: '/products', query }
  }
  
  // 否則返回基本路徑
  return '/products'
}

// 當組件掛載時載入數據 - 優化版本
onMounted(async () => {
  // 從URL參數恢復狀態
  restoreStateFromUrl()
  
  // 載入商品數據
  loadProducts()
})

// 新增：商品狀態篩選器處理函數
const handleStatusFilterChange = () => {
  pagination.currentPage = 1 // 重置到第一頁
  
  // 更新URL參數
  updateUrlParams({ 
    page: null, // 重置頁碼
    status: filters.status === 'active' ? null : filters.status // 預設為active，不需要在URL中顯示
  })
  
  loadProducts()
}

// 新增：重置所有篩選條件
const resetFilters = () => {
  // 重置所有過濾條件
  filters.name = ''
  filters.categoryId = ''
  filters.status = 'active' // 重置為預設值
  
  // 重置分頁到第一頁
  pagination.currentPage = 1
  
  // 清除所有URL參數
  updateUrlParams({ 
    keyWord: null,
    categoryId: null,
    status: null,
    page: null,
    pageSize: pagination.pageSize !== 20 ? pagination.pageSize : null
  })
  
  loadProducts()
}
</script>

<style scoped>
/* 篩選器統一樣式 */
.filter-input,
.filter-select {
  @apply w-full h-10 px-3 py-2 border-2 border-gray-400 rounded-md shadow-sm bg-white text-sm;
  @apply focus:border-blue-500 focus:ring focus:ring-blue-300 focus:ring-opacity-50;
  @apply appearance-none;
  line-height: 1.5rem;
}

.filter-button {
  @apply h-10 px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold rounded-md;
  @apply flex items-center justify-center text-sm;
  line-height: 1.5rem;
}

.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded inline-flex items-center;
}

.btn-blue {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded;
}

.btn-gray {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2 px-4 rounded h-10;
}

.btn-red {
  @apply bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded;
}

.btn-gray-sm {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-1 px-2 rounded;
}

.form-input,
.form-select {
  @apply w-full rounded-md border-2 border-gray-400 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-300 focus:ring-opacity-50 bg-white px-3 py-2 h-10;
}

.th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.page-number {
  @apply cursor-pointer px-3 py-1 rounded text-sm;
}

.spinner {
  @apply mx-auto h-8 w-8 border-4 border-blue-200 rounded-full border-t-blue-600 animate-spin;
}

/* 骨架屏動畫 */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse div {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* 表格載入狀態 */
.table-loading {
  position: relative;
}

.table-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* 圖片懶載入優化 */
img[loading="lazy"] {
  transition: opacity 0.3s ease;
}

img[loading="lazy"]:not([src]) {
  opacity: 0;
}

/* 響應式優化 */
@media (max-width: 768px) {
  .pagination-controls {
    flex-direction: column;
    gap: 1rem;
  }
  
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* 特色商品切換開關樣式 */
.toggle-checkbox:checked {
  @apply right-0 border-blue-600;
}

.toggle-checkbox:checked + .toggle-label {
  @apply bg-blue-600;
}

.toggle-checkbox {
  @apply right-0 border-4 border-gray-300;
}

.toggle-label {
  @apply block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer;
}

/* 商品狀態切換開關樣式 */
.toggle-checkbox-active:checked {
  @apply right-0 border-green-600;
}

.toggle-checkbox-active:checked + .toggle-label-active {
  @apply bg-green-600;
}

.toggle-checkbox-active {
  @apply right-0 border-4 border-gray-300;
}

.toggle-label-active {
  @apply block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer;
}
</style>
