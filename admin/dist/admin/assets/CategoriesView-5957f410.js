import{f as x,r as te,c as V,o as ne,S as le,U as u,V as c,$ as n,G as b,k as re,a4 as ae,a1 as C,m as M,a0 as j,a5 as F,a2 as w,a7 as oe,F as ie,a6 as de,B as P}from"./vendor-91c90871.js";import{a as ue}from"./api-b0b79693.js";import"./utils-95cec1c7.js";import"./ant-design-48c6fae4.js";import"./index-e60ec88c.js";import"./apiConfig-ad0108da.js";const ce={class:"categories-admin"},me={class:"bg-white rounded-lg shadow overflow-hidden"},pe={key:0,class:"p-6 text-center"},fe={key:1,class:"p-6 text-center text-gray-500"},ve={key:2,class:"p-4"},ge={class:"category-tree-node flex items-center justify-between w-full py-2 pr-3"},he={class:"font-medium"},_e={class:"flex items-center space-x-3"},be={key:0,class:"text-sm text-gray-500 mr-3"},ye={class:"flex space-x-2"},xe=["onClick","title"],Ce=["onClick","disabled"],we=["onClick"],ke={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"},De={class:"bg-white rounded-lg shadow-lg w-full max-w-lg p-6"},$e={class:"text-xl font-semibold mb-4"},Ae={class:"space-y-4"},Ne={class:"form-group"},Ee=["disabled"],Ve={key:0,class:"text-gray-500"},Me={key:0,class:"form-group"},Se=["value"],je={key:1,class:"form-group"},Pe={class:"p-3 bg-blue-50 border border-blue-200 rounded-md text-blue-800"},Te={class:"form-group"},Be={class:"form-group"},Le={class:"mt-6 flex justify-end space-x-3"},Ue=["disabled"],Fe={key:0},Ie={key:1},Ke={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"},Oe={class:"bg-white rounded-lg shadow-lg w-full max-w-md p-6"},ze={class:"mt-6 flex justify-end space-x-3"},Xe={__name:"CategoriesView",setup(Ge){const{getAdminApiData:I,postAdminApiData:K,updateAdminApiData:T,deleteAdminApiData:O}=ue(),_=x([]),S=x(!0),$=x(!1),A=x(!1),d=x(!1),k=x(!1),t=te({id:null,name:"",description:"",parent_id:null,sort_order:0}),D=x(null),z=V(()=>B(_.value)),G=V(()=>{const s=[],e=r=>{r.forEach(a=>{s.push(a.id.toString()),a.children&&a.children.length>0&&e(a.children)})};return e(_.value),s});ne(()=>{N()});const N=async()=>{S.value=!0;try{const s=await I("admin/categories");s.success?(_.value=s.data,console.log("載入分類成功:",_.value.length,"個分類")):console.error("載入分類失敗:",s.message)}catch(s){console.error("載入分類時出錯:",s)}finally{S.value=!1}},m=s=>E().find(r=>r.id.toString()===s.toString()),B=s=>s.map(e=>({key:e.id.toString(),title:e.name,children:e.children&&e.children.length>0?B(e.children):void 0,selectable:!1,isLeaf:!e.children||e.children.length===0})),E=()=>{const s=[],e=(r,a=null)=>{r.forEach(l=>{s.push({...l,parentName:a}),l.children&&l.children.length>0&&e(l.children,l.name)})};return e(_.value),s},L=s=>{let e="",r=s,a=0;for(;r.parentName;){a++;const l=E().find(i=>i.name===r.parentName);if(!l)break;r=l}return a>0&&(e="　".repeat(a)+"└ "),`${e}${s.name}`};V(()=>{const s=[],e=(r,a=0)=>{s.push({...r,level:a}),r.children&&r.children.length>0&&r.children.forEach(l=>{e(l,a+1)})};return _.value.forEach(r=>{e(r)}),s});const q=V(()=>{if(!t.id)return E().map(r=>({id:r.id,levelName:L(r)}));const s=[],e=W(t.id);return E().forEach(r=>{e.includes(r.id)||s.push({id:r.id,levelName:L(r)})}),s}),W=s=>{const e=[s],r=(a,l)=>{for(const i of a){if(i.id===l)return i.children&&i.children.length>0&&i.children.forEach(p=>{e.push(p.id),r(a,p.id)}),!0;if(i.children&&i.children.length>0&&r(i.children,l))return!0}return!1};return r(_.value,s),e},H=s=>{s&&(t.id=s.id,t.name=s.name,t.description=s.description||"",s.name==="商品館"||s.name==="品牌館"?t.parent_id=null:t.parent_id=s.parent_id,t.sort_order=s.sort_order||0,d.value=!0)},J=()=>{t.id=null,t.name="",t.description="",t.parent_id=null,t.sort_order=0,A.value=!0},Q=s=>{s&&(t.id=null,t.name="",t.description="",t.parent_id=s.id,t.sort_order=0,A.value=!0)},R=async s=>{const e=s.node.key,r=s.dragNode.key,a=s.dropPosition;s.node.pos.split("-");const l=s.dropPosition-1,i=m(r);if((i.name==="商品館"||i.name==="品牌館")&&!s.dropToGap){alert("「商品館」和「品牌館」是系統核心分類，不能作為其他分類的子項");return}const p=(o,f,y)=>{for(let g=0;g<o.length;g++){if(o[g].id.toString()===f)return y(o[g],g,o);o[g].children&&p(o[g].children,f,y)}},h=[..._.value];let v;if(p(h,r,(o,f,y)=>{y.splice(f,1),v=o}),!s.dropToGap)p(h,e,o=>{o.children=o.children||[],o.children.unshift(v),v.parent_id=o.id});else if((s.node.children||[]).length>0&&s.node.expanded&&a===1)p(h,e,o=>{o.children=o.children||[],o.children.unshift(v),v.parent_id=o.id});else{let o,f;p(h,e,(y,g,se)=>{o=se,f=g}),l===-1?(o.splice(f,0,v),v.parent_id=o[f+1].parent_id):(o.splice(f+1,0,v),v.parent_id=o[f].parent_id)}_.value=h,await X(h)},X=async s=>{try{const e=[],r=(a,l=null)=>{a.forEach((i,p)=>{const h=i.name==="商品館"||i.name==="品牌館"?null:l;e.push(T(`admin/categories/${i.id}`,{name:i.name,description:i.description||"",parent_id:h,sort_order:p})),i.children&&i.children.length>0&&r(i.children,i.id)})};r(s);for(let a=0;a<e.length;a++){const l=await e[a];l.success||console.error(`分類 #${a} 更新失敗:`,l.message)}await N()}catch(e){console.error("更新分類順序失敗:",e),alert("更新分類順序時發生錯誤: "+(e.message||"未知錯誤"))}},Y=s=>{if(s){if(s.name==="商品館"||s.name==="品牌館"){alert("「商品館」和「品牌館」是系統核心分類，不可刪除");return}D.value=s,k.value=!0}},Z=async()=>{if(D.value.name==="商品館"||D.value.name==="品牌館"){alert("「商品館」和「品牌館」是系統核心分類，不可刪除"),k.value=!1;return}try{const s=await O(`admin/categories/${D.value.id}`);s.success?(N(),k.value=!1,alert("分類已成功刪除")):s.message&&s.message.includes("no such column: sort")?alert("刪除失敗：後端資料庫結構需要更新。請聯繫技術人員更新遠端後端代碼。"):alert(`刪除失敗: ${s.message}`)}catch(s){console.error("刪除分類出錯:",s),s.response&&s.response.data&&s.response.data.message?s.response.data.message.includes("no such column: sort")?alert(`刪除失敗：後端資料庫結構需要更新。

技術說明：後端代碼仍在查詢已移除的 sort 欄位，需要更新遠端伺服器上的後端代碼。`):alert(`刪除失敗: ${s.response.data.message}`):alert("刪除分類時發生錯誤，請稍後再試")}},U=()=>{A.value=!1,d.value=!1,t.id=null,t.name="",t.description="",t.parent_id=null,t.sort_order=0},ee=async()=>{if(!t.name.trim()){alert("請填寫分類名稱");return}if(!t.parent_id&&!d.value){alert("分類最高層級只能是「商品館」和「品牌館」，請選擇一個上層分類");return}if(!t.parent_id&&d.value&&t.name!=="商品館"&&t.name!=="品牌館"){alert("分類最高層級只能是「商品館」和「品牌館」，請選擇一個上層分類");return}const s=d.value?m(t.id):null;if(s&&(s.name==="商品館"||s.name==="品牌館")&&s.name!==t.name){alert("「商品館」和「品牌館」是系統核心分類，名稱不可修改"),t.name=s.name;return}$.value=!0;try{const e={name:t.name.trim(),description:t.description.trim(),parent_id:t.parent_id,sort_order:t.sort_order||0};let r;if(d.value?(s&&(s.name==="商品館"||s.name==="品牌館")&&(e.name=s.name,e.parent_id=null),r=await T(`admin/categories/${t.id}`,e)):r=await K("admin/categories",e),r.success)await N(),U(),alert(d.value?"分類更新成功！":"分類新增成功！");else throw new Error(r.message||"操作失敗")}catch(e){console.error("保存分類失敗:",e),alert(`保存分類失敗: ${e.message||"未知錯誤"}`)}finally{$.value=!1}};return(s,e)=>{var a;const r=le("a-tree");return u(),c("div",ce,[n("div",{class:"mb-6 flex justify-between items-center"},[e[6]||(e[6]=n("div",{class:"text-sm text-gray-600"},[n("i",{class:"fas fa-info-circle mr-1"}),b(" 注意：分類最高層級只有品牌館和商品館，不能新增其他最高層級。新增分類只能在商品館或品牌館下添加子類別。 ")],-1)),n("button",{onClick:J,class:"btn-primary"},e[5]||(e[5]=[n("i",{class:"fas fa-plus mr-2"},null,-1),b(" 新增分類 ")]))]),n("div",me,[S.value?(u(),c("div",pe,e[7]||(e[7]=[n("div",{class:"spinner"},null,-1),n("p",{class:"mt-2 text-gray-600"},"載入中...",-1)]))):_.value.length===0?(u(),c("div",fe,e[8]||(e[8]=[n("i",{class:"fas fa-folder text-5xl mb-4"},null,-1),n("p",null,"暫無分類資料",-1)]))):(u(),c("div",ve,[re(r,{class:"category-tree","tree-data":z.value,"default-expanded-keys":G.value,draggable:"","block-node":"","show-line":"",onDrop:R},{title:ae(({key:l,title:i})=>{var p,h,v,o,f,y;return[n("div",ge,[n("span",he,C(i),1),n("div",_e,[m(l)?(u(),c("span",be," 排序: "+C(m(l).sort_order||0),1)):w("",!0),n("div",ye,[n("button",{onClick:P(g=>H(m(l)),["stop"]),class:"btn-blue-sm",title:((p=m(l))==null?void 0:p.name)==="商品館"||((h=m(l))==null?void 0:h.name)==="品牌館"?"僅可編輯排序和描述，名稱不可更改":"編輯分類"},e[9]||(e[9]=[n("i",{class:"fas fa-edit mr-1"},null,-1),b(" 編輯 ")]),8,xe),n("button",{onClick:P(g=>Y(m(l)),["stop"]),class:F(["btn-red-sm",{"opacity-50 cursor-not-allowed":((v=m(l))==null?void 0:v.name)==="商品館"||((o=m(l))==null?void 0:o.name)==="品牌館"}]),disabled:((f=m(l))==null?void 0:f.name)==="商品館"||((y=m(l))==null?void 0:y.name)==="品牌館"},e[10]||(e[10]=[n("i",{class:"fas fa-trash mr-1"},null,-1),b(" 刪除 ")]),10,Ce),n("button",{onClick:P(g=>Q(m(l)),["stop"]),class:"btn-green-sm"},e[11]||(e[11]=[n("i",{class:"fas fa-plus mr-1"},null,-1),b(" 新增子類 ")]),8,we)])])])]}),_:1},8,["tree-data","default-expanded-keys"])]))]),A.value||d.value?(u(),c("div",ke,[n("div",De,[n("h3",$e,C(d.value?"編輯分類":"新增分類"),1),n("div",Ae,[n("div",Ne,[e[12]||(e[12]=n("label",{for:"categoryName",class:"form-label"},[b("分類名稱 "),n("span",{class:"text-red-500"},"*")],-1)),M(n("input",{id:"categoryName","onUpdate:modelValue":e[0]||(e[0]=l=>t.name=l),type:"text",class:F(["form-input",{"bg-gray-100 cursor-not-allowed":d.value&&(t.name==="商品館"||t.name==="品牌館")}]),required:"",disabled:d.value&&(t.name==="商品館"||t.name==="品牌館")},null,10,Ee),[[j,t.name]]),d.value&&(t.name==="商品館"||t.name==="品牌館")?(u(),c("small",Ve,"「商品館」和「品牌館」是系統核心分類，名稱不可修改")):w("",!0)]),d.value&&(t.name==="商品館"||t.name==="品牌館")?w("",!0):(u(),c("div",Me,[e[13]||(e[13]=n("label",{for:"parentCategory",class:"form-label"},"上層分類",-1)),M(n("select",{id:"parentCategory","onUpdate:modelValue":e[1]||(e[1]=l=>t.parent_id=l),class:"form-select"},[(u(!0),c(ie,null,de(q.value,l=>(u(),c("option",{key:l.id,value:l.id},C(l.levelName),9,Se))),128))],512),[[oe,t.parent_id]]),e[14]||(e[14]=n("small",{class:"text-gray-500"},"請選擇此分類的上層分類，最高層級只能是「商品館」和「品牌館」",-1))])),d.value&&(t.name==="商品館"||t.name==="品牌館")?(u(),c("div",je,[n("div",Pe,[e[15]||(e[15]=n("i",{class:"fas fa-info-circle mr-1"},null,-1)),b(" 「"+C(t.name)+"」為系統核心分類，固定為最高層級，無需設定上層分類。 ",1)])])):w("",!0),n("div",Te,[e[16]||(e[16]=n("label",{for:"sortOrder",class:"form-label"},"排序順序",-1)),M(n("input",{id:"sortOrder","onUpdate:modelValue":e[2]||(e[2]=l=>t.sort_order=l),type:"number",min:"0",class:"form-input"},null,512),[[j,t.sort_order,void 0,{number:!0}]]),e[17]||(e[17]=n("small",{class:"text-gray-500"},"數字越小排序越前面，默認為0",-1))]),n("div",Be,[e[18]||(e[18]=n("label",{for:"categoryDesc",class:"form-label"},"分類描述",-1)),M(n("textarea",{id:"categoryDesc","onUpdate:modelValue":e[3]||(e[3]=l=>t.description=l),class:"form-textarea",rows:"3"},null,512),[[j,t.description]])])]),n("div",Le,[n("button",{onClick:U,class:"btn-gray"},"取消"),n("button",{onClick:ee,class:"btn-blue",disabled:$.value},[$.value?(u(),c("span",Fe,"儲存中...")):(u(),c("span",Ie,e[19]||(e[19]=[n("i",{class:"fas fa-save mr-2"},null,-1),b(" 儲存")])))],8,Ue)])])])):w("",!0),k.value?(u(),c("div",Ke,[n("div",Oe,[e[21]||(e[21]=n("h3",{class:"text-xl font-semibold mb-4"},"確認刪除",-1)),n("p",null,"您確定要刪除分類「"+C((a=D.value)==null?void 0:a.name)+"」嗎？此操作無法還原。",1),e[22]||(e[22]=n("p",{class:"text-sm text-red-500 mt-2"},"注意：若分類下有子分類或商品，將無法刪除。",-1)),n("div",ze,[n("button",{onClick:e[4]||(e[4]=l=>k.value=!1),class:"btn-gray"},"取消"),n("button",{onClick:Z,class:"btn-red"},e[20]||(e[20]=[n("i",{class:"fas fa-trash mr-2"},null,-1),b(" 確認刪除 ")]))])])])):w("",!0)])}}};export{Xe as default};
