import{f as o,r as J,o as K,U as n,V as i,$ as s,G as u,F as O,a6 as X,a1 as y,m as M,a0 as U,u as j,B as Y,a2 as w,W as Z}from"./vendor-91c90871.js";import{a as ee}from"./api-b0b79693.js";import{a as z}from"./apiConfig-ad0108da.js";import{f as se}from"./style-5fb4a138.js";import{_ as te}from"./index-e60ec88c.js";import"./utils-95cec1c7.js";import"./ant-design-48c6fae4.js";const le={class:"brands-admin"},ae={class:"mb-6 flex justify-end items-center"},oe={class:"bg-white rounded-lg shadow overflow-hidden"},ne={key:0,class:"p-6 text-center"},ie={key:1,class:"p-6 text-center text-gray-500"},re={key:2},de={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6"},ue={class:"h-40 bg-gray-200 rounded-t-lg overflow-hidden"},ce=["src","alt"],me={class:"p-4"},pe={class:"text-xl font-semibold mb-2"},fe={class:"text-gray-600 mb-4 line-clamp-2"},ge={class:"flex justify-end space-x-2"},ve=["onClick"],be=["onClick"],he={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"},xe={class:"bg-white rounded-lg shadow-lg w-full max-w-lg p-6"},ye={class:"text-xl font-semibold mb-4"},we={class:"space-y-4"},_e={class:"form-group"},ke={class:"form-group"},Ce={class:"form-group"},De={class:"border-2 border-dashed border-gray-300 p-4 rounded-lg"},Be={class:"flex items-center"},$e={class:"flex-shrink-0 w-32 h-32 bg-gray-200 rounded overflow-hidden mr-4"},je=["src"],Ae={key:1,class:"w-full h-full flex items-center justify-center text-gray-400"},Ie={class:"flex-1"},Me={class:"mt-6 flex justify-end space-x-3"},Ue=["disabled"],ze={key:0},Re={key:1},Ve={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"},We={class:"bg-white rounded-lg shadow-lg w-full max-w-md p-6"},Fe={class:"mt-6 flex justify-end space-x-3"},He={key:2,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"},Le={class:"bg-white rounded-lg shadow-lg w-full max-w-4xl p-6"},Ne={class:"cropper-container",style:{height:"400px"}},Ee={__name:"BrandsView",setup(Se){const{getAdminApiData:R,postAdminApiData:A,updateAdminApiData:V,deleteAdminApiData:W}=ee(),_=o([]),k=o(!0),v=o(!1),C=o(!1),m=o(!1),b=o(!1),a=J({id:null,name:"",description:"",logo:""}),D=o(null),F=o(null),p=o(null),c=o(""),h=o(!1),f=o(null),B=o(null),H=o(null),x=o("image/jpeg"),L=o(.9),d={minWidth:200,minHeight:200,maxWidth:1e3,maxHeight:1e3,width:500,height:500,aspectRatio:null},$=async()=>{k.value=!0;try{const t=await R("admin/brands");t.success?_.value=t.brands||[]:console.error("載入品牌失敗:",t.message)}catch(t){console.error("載入品牌出錯:",t)}finally{k.value=!1}},N=t=>{const e=t.target.files[0];if(!e)return;if(!e.type.startsWith("image/")){alert(`文件 ${e.name} 不是有效的圖片格式`),t.target.value=null;return}if(e.size>2*1024*1024){alert(`文件 ${e.name} 超過了2MB的大小限制`),t.target.value=null;return}const r=new FileReader;r.onload=l=>{f.value=l.target.result,x.value=e.type,h.value=!0},r.readAsDataURL(e),t.target.value=null},E=()=>{if(!B.value)return;const{canvas:t}=B.value.getResult();t.toBlob(e=>{S(e)},x.value,L.value)},S=t=>{if(!t){console.error("無法創建裁剪後的圖片");return}console.log(`裁剪後的圖片大小: ${t.size} 字節`),p.value=new File([t],`cropped_logo_${Date.now()}.${x.value.split("/")[1]}`,{type:x.value});const e=new FileReader;e.onload=r=>{c.value=r.target.result},e.readAsDataURL(p.value),h.value=!1,f.value=null},T=()=>{h.value=!1,f.value=null},q=t=>{a.id=t.id,a.name=t.name,a.description=t.description||"",a.logo=t.logo||"",c.value=t.logo||"",m.value=!0},G=t=>{D.value=t,b.value=!0},P=async()=>{try{const t=await W("admin/brands",D.value.id);t.success?($(),b.value=!1,alert("品牌已成功刪除")):alert(`刪除失敗: ${t.message}`)}catch(t){console.error("刪除品牌出錯:",t),alert("刪除品牌時發生錯誤，請稍後再試")}},I=()=>{C.value=!1,m.value=!1,a.id=null,a.name="",a.description="",a.logo="",p.value=null,c.value=""},Q=async()=>{if(!a.name.trim()){alert("請輸入品牌名稱");return}v.value=!0;try{let t=a.logo;if(p.value){const l=new FormData;l.append("image",p.value);const g=await A("admin/upload/single?type=brand",l);if(g.success&&g.imageUrl)t=g.imageUrl;else throw new Error("Logo上傳失敗")}const e={name:a.name,description:a.description,logo:t};let r;if(m.value?r=await V("admin/brands",{id:a.id,...e}):r=await A("admin/brands",e),r.success)$(),I(),alert(m.value?"品牌更新成功！":"品牌新增成功！");else throw new Error(r.message||"操作失敗")}catch(t){console.error("保存品牌失敗:",t),alert(`操作失敗: ${t.message||"未知錯誤"}`)}finally{v.value=!1}};return K(()=>{$()}),(t,e)=>{var r;return n(),i("div",le,[s("div",ae,[s("button",{onClick:e[0]||(e[0]=l=>C.value=!0),class:"btn-primary"},e[6]||(e[6]=[s("i",{class:"fas fa-plus mr-2"},null,-1),u(" 新增品牌 ")]))]),s("div",oe,[k.value?(n(),i("div",ne,e[7]||(e[7]=[s("div",{class:"spinner"},null,-1),s("p",{class:"mt-2 text-gray-600"},"載入中...",-1)]))):_.value.length===0?(n(),i("div",ie,e[8]||(e[8]=[s("i",{class:"fas fa-building text-5xl mb-4"},null,-1),s("p",null,"暫無品牌資料",-1)]))):(n(),i("div",re,[s("div",de,[(n(!0),i(O,null,X(_.value,l=>(n(),i("div",{key:l.id,class:"bg-white rounded-lg border hover:shadow-lg transition-shadow"},[s("div",ue,[s("img",{src:j(z)(l.logo,"brand"),alt:l.name,class:"w-full h-full object-cover",onerror:"this.src=getDefaultImage()"},null,8,ce)]),s("div",me,[s("h3",pe,y(l.name),1),s("p",fe,y(l.description),1),s("div",ge,[s("button",{onClick:g=>q(l),class:"btn-blue-sm"},e[9]||(e[9]=[s("i",{class:"fas fa-edit mr-1"},null,-1),u(" 編輯 ")]),8,ve),s("button",{onClick:g=>G(l),class:"btn-red-sm"},e[10]||(e[10]=[s("i",{class:"fas fa-trash mr-1"},null,-1),u(" 刪除 ")]),8,be)])])]))),128))])]))]),C.value||m.value?(n(),i("div",he,[s("div",xe,[s("h3",ye,y(m.value?"編輯品牌":"新增品牌"),1),s("div",we,[s("div",_e,[e[11]||(e[11]=s("label",{for:"brandName",class:"form-label"},[u("品牌名稱 "),s("span",{class:"text-red-500"},"*")],-1)),M(s("input",{id:"brandName","onUpdate:modelValue":e[1]||(e[1]=l=>a.name=l),type:"text",class:"form-input",required:""},null,512),[[U,a.name]])]),s("div",ke,[e[12]||(e[12]=s("label",{for:"brandDesc",class:"form-label"},"品牌描述",-1)),M(s("textarea",{id:"brandDesc","onUpdate:modelValue":e[2]||(e[2]=l=>a.description=l),class:"form-textarea",rows:"3"},null,512),[[U,a.description]])]),s("div",Ce,[e[16]||(e[16]=s("label",{class:"form-label"},"品牌Logo",-1)),s("div",De,[s("div",Be,[s("div",$e,[c.value?(n(),i("img",{key:0,src:c.value.startsWith("data:")?c.value:j(z)(c.value,"brand"),alt:"預覽",class:"w-full h-full object-cover",onerror:"this.src=getDefaultImage()"},null,8,je)):(n(),i("div",Ae,e[13]||(e[13]=[s("i",{class:"fas fa-image text-4xl"},null,-1)])))]),s("div",Ie,[s("input",{type:"file",ref_key:"logoInput",ref:F,accept:"image/*",class:"hidden",onChange:N},null,544),s("button",{onClick:e[3]||(e[3]=Y(l=>t.$refs.logoInput.click(),["prevent"])),class:"btn-blue-outline w-full mb-2"},e[14]||(e[14]=[s("i",{class:"fas fa-cloud-upload-alt mr-2"},null,-1),u(" 選擇圖片 ")])),e[15]||(e[15]=s("p",{class:"text-sm text-gray-500"},"上傳後可自由裁切圖片，大小不超過2MB",-1))])])])])]),s("div",Me,[s("button",{onClick:I,class:"btn-gray"},"取消"),s("button",{onClick:Q,class:"btn-blue",disabled:v.value},[v.value?(n(),i("span",ze,"儲存中...")):(n(),i("span",Re,e[17]||(e[17]=[s("i",{class:"fas fa-save mr-2"},null,-1),u(" 儲存")])))],8,Ue)])])])):w("",!0),b.value?(n(),i("div",Ve,[s("div",We,[e[19]||(e[19]=s("h3",{class:"text-xl font-semibold mb-4"},"確認刪除",-1)),s("p",null,"您確定要刪除品牌「"+y((r=D.value)==null?void 0:r.name)+"」嗎？此操作無法還原。",1),s("div",Fe,[s("button",{onClick:e[4]||(e[4]=l=>b.value=!1),class:"btn-gray"},"取消"),s("button",{onClick:P,class:"btn-red"},e[18]||(e[18]=[s("i",{class:"fas fa-trash mr-2"},null,-1),u(" 確認刪除 ")]))])])])):w("",!0),h.value?(n(),i("div",He,[s("div",Le,[e[21]||(e[21]=s("h3",{class:"text-xl font-semibold mb-4"},"裁剪圖片",-1)),e[22]||(e[22]=s("p",{class:"text-sm text-gray-500 mb-4"},"您可以自由調整裁剪框的大小和位置，建議尺寸為 500x500 像素",-1)),s("div",Ne,[f.value?(n(),Z(j(se),{key:0,ref_key:"cropper",ref:B,src:f.value,"stencil-props":{aspectRatio:d.aspectRatio,width:d.width,height:d.height,minWidth:d.minWidth,minHeight:d.minHeight,maxWidth:d.maxWidth,maxHeight:d.maxHeight,movable:!0,resizable:!0},"default-size":{width:d.width,height:d.height},"default-position":{left:.5,top:.5},"resize-image":{adjustStencil:!1},onChange:e[5]||(e[5]=l=>H.value=l),class:"w-full h-full"},null,8,["src","stencil-props","default-size"])):w("",!0)]),s("div",{class:"mt-6 flex justify-end space-x-3"},[s("button",{onClick:T,class:"btn-gray"},"取消"),s("button",{onClick:E,class:"btn-blue"},e[20]||(e[20]=[s("i",{class:"fas fa-crop-alt mr-2"},null,-1),u(" 確認裁剪 ")]))])])])):w("",!0)])}}},Oe=te(Ee,[["__scopeId","data-v-59ecb8b7"]]);export{Oe as default};
