import{f as y,U as u,V as d,$ as t,F as D,a6 as T,a1 as m,a2 as K,m as O,a0 as R,a8 as q,c as $,o as z,S as N,G as M,k as A,a4 as E,a5 as S,B as G}from"./vendor-91c90871.js";import{a as W}from"./api-b0b79693.js";import{_ as V}from"./index-e60ec88c.js";import{m as a,M as H}from"./ant-design-48c6fae4.js";import"./utils-95cec1c7.js";import"./apiConfig-ad0108da.js";const J={class:"chat-container"},P={class:"messages-container"},Q={class:"text-xs text-gray-400 mb-1"},X={class:"flex items-start"},Y={class:"ml-2"},Z={class:"bg-gray-100 rounded-lg px-4 py-2 text-gray-800 max-w-xs shadow-sm"},ee={class:"text-xs text-gray-500 mt-1"},te={key:0,class:"flex items-start justify-end mt-2"},se={class:"mr-2 text-right"},oe={class:"bg-blue-100 rounded-lg px-4 py-2 text-blue-800 max-w-xs shadow-sm"},ae={class:"reply-container"},ne={class:"flex mt-4"},le={__name:"AdminChatMessageList",props:{messages:{type:Array,required:!0},formatDateTime:{type:Function,required:!0}},emits:["reply"],setup(h,{emit:b}){const x=h,w=b,p=y(""),l=()=>{if(!p.value.trim()||x.messages.length===0)return;const n=x.messages[x.messages.length-1];w("reply",{id:n.id,reply:p.value}),p.value=""};return(n,c)=>(u(),d("div",J,[t("div",P,[(u(!0),d(D,null,T(h.messages,i=>(u(),d("div",{key:i.id,class:"mb-6"},[t("div",Q,m(i.product?"商品留言："+(i.productName||"未知商品"):"一般留言"),1),t("div",X,[c[1]||(c[1]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-user-circle text-3xl text-gray-400"})],-1)),t("div",Y,[t("div",Z,m(i.content),1),t("div",ee,m(h.formatDateTime(i.createdAt)),1)])]),i.reply?(u(),d("div",te,[t("div",se,[t("div",oe,m(i.reply),1),c[2]||(c[2]=t("div",{class:"text-xs text-gray-400 mt-1"},"管理員回覆",-1))]),c[3]||(c[3]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-user-shield text-3xl text-blue-400"})],-1))])):K("",!0)]))),128))]),t("div",ae,[t("div",ne,[O(t("input",{"onUpdate:modelValue":c[0]||(c[0]=i=>p.value=i),type:"text",class:"border rounded-l px-4 py-2 w-full",placeholder:"輸入回覆內容...",onKeyup:q(l,["enter"])},null,544),[[R,p.value]]),t("button",{class:"bg-blue-500 text-white px-4 py-2 rounded-r hover:bg-blue-600",onClick:l},"回覆")])])]))}},re=V(le,[["__scopeId","data-v-6de90a6d"]]);const ie={class:"comments-admin flex"},ce={class:"w-1/4 border-r bg-gray-50 min-h-[720px]"},ue=["onClick"],de={class:"font-medium"},me={class:"text-xs text-gray-500"},fe={class:"flex-shrink-0"},ve=["onClick"],pe={class:"w-3/4 p-6 relative",style:{height:"90vh",overflow:"hidden"}},xe={key:0,class:"h-full flex flex-col"},_e={class:"flex justify-between items-center mb-4"},ye={class:"text-xl font-bold"},he={class:"flex-1 overflow-auto pb-16"},ge={key:1,class:"h-full flex flex-col"},be={class:"flex justify-between items-center mb-4"},we={class:"text-xl font-bold"},Ce={key:2,class:"text-gray-400 flex items-center justify-center h-full"},ke={class:"mb-4"},$e={class:"font-medium mb-2"},Me={__name:"CommentsView",setup(h){const{getAdminApiData:b,patchAdminApiData:x,deleteAdminApiData:w,postAdminApiData:p}=W(),l=y([]),n=y(null),c=$(()=>{if(!l.value||l.value.length===0)return[];const e={};return l.value.forEach(s=>{e[s.user]?e[s.user].messageCount++:e[s.user]={userId:s.user,userName:s.userName||"未知用戶",messageCount:1}}),Object.values(e)}),i=$(()=>!l.value||!n.value?[]:l.value.filter(e=>e.user===n.value).sort((e,s)=>new Date(e.createdAt)-new Date(s.createdAt))),C=$(()=>{if(!n.value)return"";const e=c.value.find(s=>s.userId===n.value);return e?e.userName:"未知用戶"}),g=y(!1),_=y(""),k=()=>{_.value="",g.value=!0},j=async()=>{if(!_.value.trim()){a.warning("請輸入留言內容");return}if(!n.value){a.warning("請先選擇用戶");return}try{const e=await p("admin/comments",{userId:n.value,content:_.value,isAdminMessage:!0});e&&e.success?(a.success("留言發送成功"),await I(),g.value=!1):a.error((e==null?void 0:e.message)||"留言發送失敗")}catch(e){console.error("發送留言時發生錯誤:",e),a.error("發送留言失敗，請稍後再試")}},I=async()=>{try{const e=await b("admin/comments");e&&e.success?l.value=e.comments:a.error((e==null?void 0:e.message)||"獲取留言列表失敗")}catch(e){console.error("獲取留言列表時發生錯誤:",e),a.error("獲取留言列表失敗，請稍後再試")}},U=e=>{H.confirm({title:"確認刪除",content:`確定要刪除「${e.userName}」的所有留言嗎？此操作無法恢復。`,okText:"確認",okType:"danger",cancelText:"取消",onOk:()=>L(e.userId)})},L=async e=>{try{const s=l.value.filter(o=>o.user===e).map(o=>o.id);if(s.length===0){a.info("沒有找到該用戶的留言");return}const r=`delete_user_${e}`;a.loading({content:"正在刪除留言...",key:r,duration:0});let f=0;for(const o of s)try{const v=await w(`admin/comments/${o}`);v&&(v.success===!0||v.status===200)?f++:console.warn(`留言ID ${o} 刪除失敗:`,v)}catch(v){console.error(`刪除留言 ID ${o} 失敗:`,v)}f>0?(a.success({content:`成功刪除 ${f} 則留言`,key:r}),l.value=l.value.filter(o=>o.user!==e),n.value===e&&(n.value=null)):a.error({content:"刪除留言失敗",key:r})}catch(s){console.error("刪除用戶留言時發生錯誤:",s),a.error("刪除用戶留言失敗，請稍後再試")}},B=async({id:e,reply:s})=>{if(!s.trim()){a.warning("請輸入回覆內容");return}try{const r=await x(`admin/comments/${e}/reply`,{reply:s});if(r&&r.success){a.success("回覆成功");const f=l.value.find(o=>o.id===e);f&&(f.reply=s)}else a.error((r==null?void 0:r.message)||"回覆失敗")}catch(r){console.error("回覆留言時發生錯誤:",r),a.error("回覆失敗，請稍後再試")}},F=e=>e?new Date(e).toLocaleString("zh-TW",{hour12:!1}):"-";return z(()=>{I()}),(e,s)=>{const r=N("a-textarea"),f=N("a-modal");return u(),d(D,null,[t("div",ie,[t("div",ce,[(u(!0),d(D,null,T(c.value,o=>(u(),d("div",{key:o.userId,class:S(["p-4 border-b flex justify-between items-center",n.value===o.userId?"bg-blue-100 font-bold":"hover:bg-blue-50"])},[t("div",{class:"flex-grow cursor-pointer",onClick:v=>n.value=o.userId},[t("div",de,m(o.userName),1),t("div",me,m(o.messageCount)+"則留言",1)],8,ue),t("div",fe,[t("button",{class:"text-red-500 hover:bg-red-100 p-1 rounded-full flex items-center justify-center transition-colors",onClick:G(v=>U(o),["stop"]),title:"刪除此用戶所有留言",style:{width:"28px",height:"28px"}},s[2]||(s[2]=[t("i",{class:"fas fa-trash-alt text-sm"},null,-1)]),8,ve)])],2))),128))]),t("div",pe,[n.value&&i.value.length>0?(u(),d("div",xe,[t("div",_e,[t("h2",ye,m(C.value)+"的留言",1),t("button",{class:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors flex items-center",onClick:k},s[3]||(s[3]=[t("i",{class:"fas fa-plus mr-1"},null,-1),M(" 新增留言 ")]))]),t("div",he,[A(re,{messages:i.value,formatDateTime:F,onReply:B},null,8,["messages"])])])):n.value?(u(),d("div",ge,[t("div",be,[t("h2",we,m(C.value)+"的留言",1),t("button",{class:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors flex items-center",onClick:k},s[4]||(s[4]=[t("i",{class:"fas fa-plus mr-1"},null,-1),M(" 新增留言 ")]))]),t("div",{class:"flex-1 flex items-center justify-center"},[t("div",{class:"text-center py-8 border rounded-lg w-full max-w-md"},[s[6]||(s[6]=t("p",{class:"text-gray-500"},"此用戶還沒有任何留言",-1)),t("button",{class:"border border-blue-500 text-blue-600 hover:bg-blue-50 font-medium py-2 px-4 rounded mt-4",onClick:k},s[5]||(s[5]=[t("i",{class:"fas fa-plus mr-1"},null,-1),M(" 立即發表留言 ")]))])])])):(u(),d("div",Ce," 請選擇用戶查看留言 "))])]),A(f,{open:g.value,"onUpdate:open":s[1]||(s[1]=o=>g.value=o),title:"新增管理員留言",maskClosable:!1,onOk:j,okText:"發送",cancelText:"取消"},{default:E(()=>[t("div",ke,[t("div",$e,"發送給："+m(C.value),1),A(r,{value:_.value,"onUpdate:value":s[0]||(s[0]=o=>_.value=o),placeholder:"請輸入留言內容...",rows:5,maxLength:500,showCount:""},null,8,["value"])])]),_:1},8,["open"])],64)}}},je=V(Me,[["__scopeId","data-v-e5e2dc1b"]]);export{je as default};
