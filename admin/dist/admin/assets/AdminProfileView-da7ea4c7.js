import{f as p,r as k,c as z,o as I,U as i,V as d,$ as s,aa as S,G as m,B as M,m as P,a0 as N,a5 as u,a1 as c,a2 as _,ai as j}from"./vendor-91c90871.js";import{a as R}from"./api-b0b79693.js";import{_ as G}from"./index-e60ec88c.js";import{m as f}from"./ant-design-48c6fae4.js";import"./utils-95cec1c7.js";import"./apiConfig-ad0108da.js";const L={class:"admin-profile"},W={class:"bg-white rounded-lg shadow p-6"},Z={key:0,class:"flex justify-center items-center py-8"},H={key:1,class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},J={class:"space-y-6"},K={class:"bg-gray-50 rounded-lg p-6"},Q={key:0,class:"text-red-500 text-sm mt-1"},X={key:0,class:"text-red-500 text-sm mt-1"},Y={class:"flex justify-end space-x-3 pt-4"},ss=["disabled"],es=["disabled"],ts={key:0,class:"fas fa-spinner fa-spin mr-2"},as={key:1,class:"fas fa-save mr-2"},rs={class:"bg-gray-50 rounded-lg p-6"},os={class:"space-y-3"},ls={class:"flex justify-between"},ns={class:"text-sm font-medium"},is={class:"flex justify-between"},ds={class:"inline-flex px-2 py-1 rounded-full text-xs bg-green-100 text-green-800"},ms={class:"flex justify-between"},cs={class:"text-sm font-medium"},us={class:"space-y-6"},fs={class:"bg-gray-50 rounded-lg p-6"},ps={class:"relative"},vs=["type"],ws={key:0,class:"text-red-500 text-sm mt-1"},xs={class:"relative"},ys=["type"],bs={key:0,class:"text-red-500 text-sm mt-1"},gs={class:"relative"},Ps=["type"],_s={key:0,class:"text-red-500 text-sm mt-1"},ks={class:"flex justify-end space-x-3 pt-4"},hs=["disabled"],Vs=["disabled"],js={key:0,class:"fas fa-spinner fa-spin mr-2"},As={key:1,class:"fas fa-key mr-2"},Cs={__name:"AdminProfileView",setup(Ds){const{getAdminApiData:U,updateAdminApiData:A}=R(),h=p(!0),v=p(!1),w=p(!1),x=p({}),o=k({name:"",mail:""}),a=k({currentPassword:"",newPassword:"",confirmPassword:""}),y=p(!1),b=p(!1),g=p(!1),l=k({}),r=k({}),C=async()=>{h.value=!0;try{const t=await U("admin/users/profile");t.success?(x.value=t.admin,o.name=t.admin.name,o.mail=t.admin.mail||""):f.error(t.message||"載入個人資料失敗")}catch(t){console.error("載入管理員資料錯誤:",t),f.error("載入個人資料失敗，請稍後再試")}finally{h.value=!1}},D=()=>{r.newPassword="",a.newPassword&&a.newPassword.length<6&&(r.newPassword="密碼長度至少6個字符"),V()},V=()=>{r.confirmPassword="",a.confirmPassword&&a.newPassword!==a.confirmPassword&&(r.confirmPassword="新密碼與確認密碼不符")},q=z(()=>a.currentPassword&&a.newPassword&&a.confirmPassword&&a.newPassword===a.confirmPassword&&a.newPassword.length>=6),F=async()=>{if(Object.keys(l).forEach(e=>l[e]=""),!o.name.trim()){l.name="姓名為必填項";return}if(!o.mail.trim()){l.mail="登入帳號為必填項";return}if(!/^[a-zA-Z0-9._]{3,}$/.test(o.mail.trim())){l.mail="帳號格式不正確，至少3個字符，只能包含字母、數字、底線、點號";return}v.value=!0;try{const e=await A("admin/users/profile",{name:o.name.trim(),mail:o.mail.trim()});e.success?(f.success("個人資料更新成功"),await C()):f.error(e.message||"更新失敗")}catch(e){console.error("更新個人資料錯誤:",e),f.error("更新失敗，請稍後再試")}finally{v.value=!1}},$=async()=>{if(Object.keys(r).forEach(t=>r[t]=""),D(),V(),!(r.newPassword||r.confirmPassword)){w.value=!0;try{const t=await A("admin/users/profile/password",{currentPassword:a.currentPassword,newPassword:a.newPassword,confirmPassword:a.confirmPassword});t.success?(f.success("密碼更新成功"),E()):t.message.includes("當前密碼")?r.currentPassword=t.message:f.error(t.message||"更新失敗")}catch(t){console.error("更新密碼錯誤:",t),f.error("更新失敗，請稍後再試")}finally{w.value=!1}}},B=()=>{o.name=x.value.name,o.mail=x.value.mail||"",Object.keys(l).forEach(t=>l[t]="")},E=()=>{a.currentPassword="",a.newPassword="",a.confirmPassword="",Object.keys(r).forEach(t=>r[t]=""),y.value=!1,b.value=!1,g.value=!1},O=t=>t?new Date(t).toLocaleDateString("zh-TW",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"-",T=t=>({active:"啟用",inactive:"停用",pending:"未審核"})[t]||"未知";return I(()=>{C()}),(t,e)=>(i(),d("div",L,[s("div",W,[e[22]||(e[22]=S('<div class="flex items-center justify-between mb-6" data-v-68d37298><h2 class="text-2xl font-bold text-gray-900" data-v-68d37298>個人資料管理</h2><div class="flex items-center space-x-2" data-v-68d37298><i class="fas fa-user-cog text-gray-500" data-v-68d37298></i><span class="text-sm text-gray-500" data-v-68d37298>管理員設定</span></div></div>',1)),h.value?(i(),d("div",Z,e[8]||(e[8]=[s("div",{class:"spinner"},null,-1),s("span",{class:"ml-2 text-gray-600"},"載入中...",-1)]))):(i(),d("div",H,[s("div",J,[s("div",K,[e[11]||(e[11]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},[s("i",{class:"fas fa-user mr-2 text-blue-600"}),m(" 基本資料 ")],-1)),s("form",{onSubmit:M(F,["prevent"]),class:"space-y-4"},[s("div",null,[e[9]||(e[9]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},[m(" 姓名 "),s("span",{class:"text-red-500"},"*")],-1)),P(s("input",{type:"text","onUpdate:modelValue":e[0]||(e[0]=n=>o.name=n),class:u(["form-input",{"border-red-500":l.name}]),placeholder:"請輸入姓名",required:""},null,2),[[N,o.name]]),l.name?(i(),d("span",Q,c(l.name),1)):_("",!0)]),s("div",null,[e[10]||(e[10]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},[m(" 登入帳號 "),s("span",{class:"text-red-500"},"*")],-1)),P(s("input",{type:"text","onUpdate:modelValue":e[1]||(e[1]=n=>o.mail=n),class:u(["form-input",{"border-red-500":l.mail}]),placeholder:"請輸入登入帳號",required:""},null,2),[[N,o.mail]]),l.mail?(i(),d("span",X,c(l.mail),1)):_("",!0)]),s("div",Y,[s("button",{type:"button",onClick:B,class:"btn-gray",disabled:v.value}," 重置 ",8,ss),s("button",{type:"submit",class:"btn-blue",disabled:v.value},[v.value?(i(),d("i",ts)):(i(),d("i",as)),m(" "+c(v.value?"更新中...":"更新資料"),1)],8,es)])],32)]),s("div",rs,[e[16]||(e[16]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},[s("i",{class:"fas fa-info-circle mr-2 text-green-600"}),m(" 帳號資訊 ")],-1)),s("div",os,[s("div",ls,[e[12]||(e[12]=s("span",{class:"text-sm text-gray-600"},"帳號:",-1)),s("span",ns,c(x.value.mail||"-"),1)]),e[15]||(e[15]=s("div",{class:"flex justify-between"},[s("span",{class:"text-sm text-gray-600"},"角色:"),s("span",{class:"inline-flex px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800"},[s("i",{class:"fas fa-crown mr-1"}),m(" 管理員 ")])],-1)),s("div",is,[e[13]||(e[13]=s("span",{class:"text-sm text-gray-600"},"帳號狀態:",-1)),s("span",ds,c(T(x.value.status)),1)]),s("div",ms,[e[14]||(e[14]=s("span",{class:"text-sm text-gray-600"},"註冊時間:",-1)),s("span",cs,c(O(x.value.createdAt)),1)])])])]),s("div",us,[s("div",fs,[e[21]||(e[21]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},[s("i",{class:"fas fa-key mr-2 text-orange-600"}),m(" 密碼修改 ")],-1)),s("form",{onSubmit:M($,["prevent"]),class:"space-y-4"},[s("div",null,[e[17]||(e[17]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},[m(" 當前密碼 "),s("span",{class:"text-red-500"},"*")],-1)),s("div",ps,[P(s("input",{type:y.value?"text":"password","onUpdate:modelValue":e[2]||(e[2]=n=>a.currentPassword=n),class:u(["form-input pr-10",{"border-red-500":r.currentPassword}]),placeholder:"請輸入當前密碼",required:""},null,10,vs),[[j,a.currentPassword]]),s("button",{type:"button",onClick:e[3]||(e[3]=n=>y.value=!y.value),class:"absolute inset-y-0 right-0 pr-3 flex items-center"},[s("i",{class:u([y.value?"fas fa-eye-slash":"fas fa-eye","text-gray-400"])},null,2)])]),r.currentPassword?(i(),d("span",ws,c(r.currentPassword),1)):_("",!0)]),s("div",null,[e[18]||(e[18]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},[m(" 新密碼 "),s("span",{class:"text-red-500"},"*")],-1)),s("div",xs,[P(s("input",{type:b.value?"text":"password","onUpdate:modelValue":e[4]||(e[4]=n=>a.newPassword=n),class:u(["form-input pr-10",{"border-red-500":r.newPassword}]),placeholder:"請輸入新密碼（至少6位）",required:"",onInput:D},null,42,ys),[[j,a.newPassword]]),s("button",{type:"button",onClick:e[5]||(e[5]=n=>b.value=!b.value),class:"absolute inset-y-0 right-0 pr-3 flex items-center"},[s("i",{class:u([b.value?"fas fa-eye-slash":"fas fa-eye","text-gray-400"])},null,2)])]),r.newPassword?(i(),d("span",bs,c(r.newPassword),1)):_("",!0)]),s("div",null,[e[19]||(e[19]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},[m(" 確認新密碼 "),s("span",{class:"text-red-500"},"*")],-1)),s("div",gs,[P(s("input",{type:g.value?"text":"password","onUpdate:modelValue":e[6]||(e[6]=n=>a.confirmPassword=n),class:u(["form-input pr-10",{"border-red-500":r.confirmPassword}]),placeholder:"請再次輸入新密碼",required:"",onInput:V},null,42,Ps),[[j,a.confirmPassword]]),s("button",{type:"button",onClick:e[7]||(e[7]=n=>g.value=!g.value),class:"absolute inset-y-0 right-0 pr-3 flex items-center"},[s("i",{class:u([g.value?"fas fa-eye-slash":"fas fa-eye","text-gray-400"])},null,2)])]),r.confirmPassword?(i(),d("span",_s,c(r.confirmPassword),1)):_("",!0)]),e[20]||(e[20]=S('<div class="bg-yellow-50 border border-yellow-200 rounded-md p-3" data-v-68d37298><div class="flex" data-v-68d37298><i class="fas fa-exclamation-triangle text-yellow-400 mr-2 mt-0.5" data-v-68d37298></i><div class="text-sm text-yellow-700" data-v-68d37298><p class="font-medium" data-v-68d37298>密碼安全提醒：</p><ul class="mt-1 list-disc list-inside space-y-1" data-v-68d37298><li data-v-68d37298>密碼長度至少6個字符</li><li data-v-68d37298>建議包含大小寫字母、數字和特殊符號</li><li data-v-68d37298>定期更換密碼以確保帳號安全</li></ul></div></div></div>',1)),s("div",ks,[s("button",{type:"button",onClick:E,class:"btn-gray",disabled:w.value}," 清空 ",8,hs),s("button",{type:"submit",class:"btn-orange",disabled:w.value||!q.value},[w.value?(i(),d("i",js)):(i(),d("i",As)),m(" "+c(w.value?"更新中...":"更新密碼"),1)],8,Vs)])],32)])])]))])]))}},Fs=G(Cs,[["__scopeId","data-v-68d37298"]]);export{Fs as default};
