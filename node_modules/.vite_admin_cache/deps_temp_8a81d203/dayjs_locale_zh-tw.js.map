{"version": 3, "sources": ["../../dayjs/locale/zh-tw.js"], "sourcesContent": ["!function(_,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],e):(_=\"undefined\"!=typeof globalThis?globalThis:_||self).dayjs_locale_zh_tw=e(_.dayjs)}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"zh-tw\",weekdays:\"星期日_星期一_星期二_星期三_星期四_星期五_星期六\".split(\"_\"),weekdaysShort:\"週日_週一_週二_週三_週四_週五_週六\".split(\"_\"),weekdaysMin:\"日_一_二_三_四_五_六\".split(\"_\"),months:\"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月\".split(\"_\"),monthsShort:\"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月\".split(\"_\"),ordinal:function(_,e){return\"W\"===e?_+\"週\":_+\"日\"},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"YYYY/MM/DD\",LL:\"YYYY年M月D日\",LLL:\"YYYY年M月D日 HH:mm\",LLLL:\"YYYY年M月D日dddd HH:mm\",l:\"YYYY/M/D\",ll:\"YYYY年M月D日\",lll:\"YYYY年M月D日 HH:mm\",llll:\"YYYY年M月D日dddd HH:mm\"},relativeTime:{future:\"%s內\",past:\"%s前\",s:\"幾秒\",m:\"1 分鐘\",mm:\"%d 分鐘\",h:\"1 小時\",hh:\"%d 小時\",d:\"1 天\",dd:\"%d 天\",M:\"1 個月\",MM:\"%d 個月\",y:\"1 年\",yy:\"%d 年\"},meridiem:function(_,e){var t=100*_+e;return t<600?\"凌晨\":t<900?\"早上\":t<1100?\"上午\":t<1300?\"中午\":t<1800?\"下午\":\"晚上\"}};return t.default.locale(d,null,!0),d}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,qBAAmB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,MAAK,SAAQ,UAAS,8BAA8B,MAAM,GAAG,GAAE,eAAc,uBAAuB,MAAM,GAAG,GAAE,aAAY,gBAAgB,MAAM,GAAG,GAAE,QAAO,wCAAwC,MAAM,GAAG,GAAE,aAAY,yCAAyC,MAAM,GAAG,GAAE,SAAQ,SAASA,IAAEC,IAAE;AAAC,eAAM,QAAMA,KAAED,KAAE,MAAIA,KAAE;AAAA,MAAG,GAAE,SAAQ,EAAC,IAAG,SAAQ,KAAI,YAAW,GAAE,cAAa,IAAG,aAAY,KAAI,mBAAkB,MAAK,uBAAsB,GAAE,YAAW,IAAG,aAAY,KAAI,mBAAkB,MAAK,sBAAqB,GAAE,cAAa,EAAC,QAAO,OAAM,MAAK,OAAM,GAAE,MAAK,GAAE,QAAO,IAAG,SAAQ,GAAE,QAAO,IAAG,SAAQ,GAAE,OAAM,IAAG,QAAO,GAAE,QAAO,IAAG,SAAQ,GAAE,OAAM,IAAG,OAAM,GAAE,UAAS,SAASA,IAAEC,IAAE;AAAC,YAAIC,KAAE,MAAIF,KAAEC;AAAE,eAAOC,KAAE,MAAI,OAAKA,KAAE,MAAI,OAAKA,KAAE,OAAK,OAAKA,KAAE,OAAK,OAAKA,KAAE,OAAK,OAAK;AAAA,MAAI,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["_", "e", "t"]}